export enum MatchGroupDataTestId {
  MATCH_GROUP = 'match-group',
  MATCH_GROUP_TABS = 'match-group-tabs',
  MATCH_GROUP_TAB_SEARCH = 'match-group-tab-search',
  MATCH_GROUP_TAB_DETAIL = 'match-group-tab-detail',
  MATCH_GROUP_TAB_TIMELINE = 'match-group-tab-timeline',
  MATCH_GROUP_BREADCRUMB = 'match-group-breadcrumb',
  EVENT_BREADCRUMB = 'event-breadcrumb',
  MATCH_GROUP_ROW = 'match-group-row-',
  MATCH_GROUP_ROW_MENU_ICON = 'match-group-row-menu-icon-',
  MATCH_GROUP_ROW_MENU_RENAME = 'match-group-row-menu-rename-',
  MATCH_GROUP_ROW_MENU_DELETE = 'match-group-row-menu-delete-',
  MATCHGROUP_EDIT = 'matchgroup-edit-',
  MATCHGROUP_DELETE = 'matchgroup-delete-',
  CONFIRM_DIALOG_MATCH_GROUP = 'confirm-dialog-matchGroup',
  CONFIRM_DIALOG_RENAME_INPUT = 'confirm-dialog-rename-input',
  CONFIRM_DIALOG = 'confirm-dialog',
  CONFIRM_DIALOG_EDIT_INPUT = 'confirm-dialog-edit-input',
  CONFIRM_DIALOG_CONFIRM_ACTION = 'confirm-dialog-confirm-action',
  POTENTIAL_MATCH_SEARCH_ROW = 'potential-match-search-row-',
  FILE_AND_FILTER_MATCHES_FILTER_SELECT = 'file-and-filter-matches-filter-select',
  POTENTIAL_MATCH_SEARCH_DROP = 'potential-match-search-drop',
  CONFIRM_DIALOG_TITLE = 'confirm-dialog-title',
  CONFIRM_DIALOG_DESCRIPTION = 'confirm-dialog-description',
  POTENTIAL_MATCH_SEARCH_BREADCRUMB = 'potential-match-search-breadcrumb',
  FILE_AND_FILTER_MATCHES__ADD_TO_BUTTON = 'file-and-filter-matches__add-to-button',
  FILE_AND_FILTER_MATCHES__DETAIL_UPLOAD_BUTTON = 'file-and-filter-matches__detail-upload-button',
  TRACKLET_SELECTION_HEADER_SELECT_ALL = 'tracklet-selection-header-select-all',
  SEARCH_RESULTS_CONFIDENCE_SLIDER = 'search-results-confidence-slider',
  SEARCH_RESULTS_CONFIDENCE_CHECKBOX = 'search-results-confidence-checkbox',
  PAGINATION = 'pagination',
  THUMBNAIL_SCALER = 'thumbnail-scaler',
  FIND_MATCHES_POPOVER = 'find-matches-popover',
  FIND_MATCHES_POPOVER_NEW_MATCH_ADD = 'find-matches-popover__new-match-add',
  FIND_MATCHES_POPOVER_NEW_MATCH_CONFIRM = 'find-matches-popover__new-match-confirm',
  FIND_MATCHES_POPOVER_CONFIRM = 'find-matches-popover__confirm'
}

export enum MatchGroupTab {
  SEARCH_RESULTS = 'Search Results',
  VERIFIED_MATCHES = 'Verified Matches',
  TIMELINE_EDITOR = 'Timeline Editor',
}

export enum MatchGroupButton {
  VIEW_MATCH_GROUP = 'View Match Group',
  EXPORT = 'Export',
  SAVE = 'Save',
  DELETE = 'Delete',
  YES_DELETE = 'Yes, Delete',
  CANCEL = 'Cancel',
}

export enum MatchGroupMenu {
  RENAME = 'Rename',
  DELETE = 'Delete',
}

export enum MatchGroupText {
  SELECT_TRACKLET = 'Select a Detection to View Details',
  DELETE_CONFIRMATION = 'Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.',
  ALL_EVENTS = 'All Events',
}
