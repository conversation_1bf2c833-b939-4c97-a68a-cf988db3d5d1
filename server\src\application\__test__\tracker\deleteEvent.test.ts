import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries, responses } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { has } from 'lodash';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;
// Mock successful axios response with Track app
mockedAxios.get.mockResolvedValue({
  data: {
    totalResults: 2,
    from: 0,
    to: 2,
    results: [
      {
        applicationId: '1',
        applicationName: 'Other App',
        applicationKey: 'other-app',
        applicationStatus: 'active',
        applicationDescription: 'Other application',
        applicationIconUrl: 'https://example.com/icon.png',
        applicationIconSvg: null,
        applicationUrl: 'https://example.com',
        oauth2RedirectUrls: null,
        permissionsRequired: null,
        createdDate: '2023-01-01',
        updatedDate: '2023-01-01',
        billingPlanId: null,
        eventEndpoint: null,
        ownerOrganizationId: 1,
        deploymentModel: 1,
        sharedWithOrganizationId: null,
        applicationCheckPermissions: false,
        public: false,
        headerbarEnabled: false,
      },
      {
        applicationId: '2',
        applicationName: 'Track',
        applicationKey: 'track-app',
        applicationStatus: 'active',
        applicationDescription: 'Track application',
        applicationIconUrl: 'https://example.com/track-icon.png',
        applicationIconSvg: null,
        applicationUrl: 'https://track.example.com',
        oauth2RedirectUrls: null,
        permissionsRequired: null,
        createdDate: '2023-01-01',
        updatedDate: '2023-01-01',
        billingPlanId: null,
        eventEndpoint: null,
        ownerOrganizationId: 1,
        deploymentModel: 1,
        sharedWithOrganizationId: null,
        applicationCheckPermissions: false,
        public: false,
        headerbarEnabled: false,
      },
    ],
  },
});

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.fetchAllSchemaIds)) {
        return Promise.resolve({
          dataRegistry: {
            schemas: {
              records: [
                {
                  id: 'schemaId',
                },
                {
                  id: 'schema',
                },
                {
                  id: 'id',
                },
                {
                  id: 'eventId',
                },
                {
                  id: 'an-event-id',
                },
                {
                  id: 'sdoId',
                },
              ],
            },
          },
        });
      }
      if (query.includes('fetchLatestSchema')) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes('getFolder')) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            contentTemplates: [
              {
                id: 'contentTemplateId',
                schemaId: 'schemaId',
                sdo: { id: 'sdoId', schemaId: 'schemaId', data: {} },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        !has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              aggregations: {
                recordingId: {
                  doc_count_error_upper_bound: 0,
                  sum_other_doc_count: 0,
                  buckets: [],
                },
              },
            },
          },
        });
      }
      if (query.includes('createStructuredData')) {
        return Promise.resolve({
          createStructuredData: {
            id: 'id',
          },
        });
      }
    }
  ),
}));

jest.mock('../../tracker/actions/searchFiles', () =>
  jest.fn(async (context: Context<object, responses.searchFiles>) => ({
    ...context,
    data: {
      ...context.data,
      searchFiles: {
        searchResults: [
          {
            id: 'id',
            fileName: 'name',
            status: 'status',
            createdBy: 'createdBy',
            length: 50,
            uploadDate: 'uploadDate',
            location: 'location',
            fileType: 'filetype',
            fileSize: 50,
            thumbnailUrl: 'thumbnailUrl',
            primaryAsset: {
              signedUri: 'signedUri',
            },
            streams: [{ uri: 'uri', protocol: 'protocol' }],
            frameRate: 24,
          },
        ],
        pageSize: 50,
        currentPage: 1,
        totalPages: 10,
        totalCount: 500,
      },
      filesCount: 500,
    },
  }))
);

jest.mock('../../tracker/actions/getMatchGroups', () =>
  jest.fn(async (context: Context<object, responses.getMatchGroups>) => ({
    ...context,
    data: {
      ...context.data,
      matchGroups: {
        pagination: {
          pageSize: 50,
          currentPage: 1,
          totalPages: 10,
          totalCount: 500,
        },
        sort: {
          field: 'field',
          direction: 'direction',
        },
        eventId: 'eventId',
        searchResults: [
          {
            id: 'id',
            name: 'name',
            eventId: 'eventId',
            searches: [],
            selectedTracklets: [],
            modifiedDateTime: 'modifiedDateTime',
            timelineProject: {
              groups: [],
            },
            generatedTimelines: [],
          },
        ],
      },
    },
  }))
);

describe('delete event', () => {
  it('delete event', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/event/an-event-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteFolderContentTemplate,
      {
        contentTemplateId: 'contentTemplateId',
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteStructuredData,
      {
        sdoId: 'sdoId',
        schemaId: 'schemaId',
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteFolder,
      {
        folderId: 'folderId',
      }
    );
  });

  it('cannot delete event w/o eventId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/event')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
