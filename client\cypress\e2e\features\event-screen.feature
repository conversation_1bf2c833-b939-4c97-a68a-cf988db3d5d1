Feature: Event Screen

  Background:
    Given The user is on Event Screen

  @e2e @event-screen
  Scenario: Verify user can move between Events and Files tab
    When The user clicks on the "Files" tab
    Then The "Files" tab should be active
    When The user clicks on the "Events" tab
    Then The "Events" tab should be active

  @e2e @event-screen
  Scenario: Verify user doesn't move to a new page when clicking on the breadcrumb
    When The user clicks the "All Events" breadcrumb
    Then The page should not navigate away

  @e2e @event-screen
  Scenario: Verify user can see an event detail when clicking on an event name
    When The user selects the "event" named "Cypress E2E Test"
    Then The following "event" details should be visible:
      | Field               | Expected Value             |
      | Event Name          | Cypress E2E Test           |
      | Date                | Date: 5/29/2025, 11:34 AM  |
      | Event Creator       | Event Creator: <PERSON><PERSON> |
      | File Count          | 0 File                     |
      | Match Group Count   | 0 Match Group              |
      | View Event Button   | View Event                 |
      | Delete Event Button | Delete Event               |

  @e2e @event-screen
  Scenario: Verify format of the event time
    Then "event" Time has format: "M/D/YYYY, h:m AM/PM"

  @e2e @event-screen
  Scenario: Verify user can create a new event
    Given The user deletes the following Events if exist: "e2e-create-test-event"
    When The user clicks upload files button
    And The user clicks on new event button
    Then The user enter "e2e-create-test-event" in the event name textbox
    And The user clicks on create event button
    Then The user should see a success snackbar with message "e2e-create-test-event"
    And The user clicks on cancel upload button
    Then The user should see "e2e-create-test-event" in the event table

  @e2e @event-screen
  Scenario: Verify user can edit event name
    Given The user deletes the following Events if exist: "e2e-edit-test-event"
    When The user creates default event name "e2e-edit-test-event"
    Then The user should see a success snackbar with message "e2e-edit-test-event"
    And The user clicks on cancel upload button
    Then The user should see "e2e-edit-test-event" in the event table
    When The user selects the "event" named "e2e-edit-test-event"
    And The user clicks the "event" name located on the right side
    And The user changes "event" name to "e2e-edit2-test-event"
    Then The name of the "event" should be updated to "e2e-edit2-test-event"

  @e2e @event-screen
  Scenario: Verify user can go to View Event Details page
    When The user enters "Cypress E2E Test" into the search bar
    When The user selects the "event" named "Cypress E2E Test"
    And The user clicks on "View Event"
    Then The user should navigate to event details page

  @e2e @event-screen
  Scenario: Verify user can delete an event successfully
    Given The user deletes the following Events if exist: "e2e-delete-test-event"
    When The user creates default event name "e2e-delete-test-event"
    Then The user should see a success snackbar with message "e2e-delete-test-event"
    And The user clicks on cancel upload button
    Then The user should see "e2e-delete-test-event" in the event table
    When The user selects the "event" named "e2e-delete-test-event"
    And The user clicks on "Delete Event"
    And The user enters wrong event name "e2e-delete-test-event"
    Then The user verifies delete button is enabled and clicks it
    Then The user should see a success snackbar with message "Successfully deleted event e2e-delete-test-event"

  @e2e @event-screen
  Scenario: Verify user enters can not delete an event with wrong event name confirmation
    When The user selects the "event" named "Cypress E2E Test"
    And The user clicks on "Delete Event"
    And The user enters wrong event name "Wrong Name"
    Then The "Delete" button should still be disabled and textbox highlighted in red

  @e2e @event-screen
  Scenario Outline: Verify user can sort table columns by <ColumnName> <SortedBy>
    When The user clicks on the "<ColumnName>" column header for "event"
    Then The "event" table should be sorted by "<ColumnName>" in "<SortedBy>" order

    Examples:
      | ColumnName | SortedBy |
      | Event Name | z-a      |
      | Event Name | a-z      |
      | Event Time | z-a      |
      | Event Time | a-z      |

  @e2e @event-screen @search
  Scenario Outline: Verify user can search for events
    When The user enters "<keyword>" into the search bar
    Then The displayed event results should contain "<keyword>"

    Examples:
      | keyword          |
      | Cypress E2E Test |

  @e2e @event-screen @search
  Scenario Outline: Verify user can search for files
    When The user clicks on the "Files" tab
    And The user enters "<keyword>" into the search bar
    Then The displayed file results should contain "<keyword>"

    Examples:
      | keyword            |
      | 4s testing tdo.mp4 |

  @e2e @event-screen
  Scenario: Verify results per page functionality
    Then The user should see the "Results Per Page" label
    When The user changes the results per page and verifies the following options:
      | PerPage |
      | 100     |
      | 10      |
      | 50      |

  @e2e @event-screen
  Scenario: Verify user can move between pages
    Then The user should see the initial pagination state
    When The user navigates to the "next" page
    Then The pagination should update for the next page
    When The user navigates to the "previous" page
    Then The pagination should return to the initial state

  @e2e @event-screen
  Scenario: Delete Events after run
    Given The user deletes the following Events if exist:
      | eventName             |
      | e2e-create-test-event |
      | e2e-edit-test-event   |
      | e2e-edit2-test-event  |
      | e2e-delete-test-event |
