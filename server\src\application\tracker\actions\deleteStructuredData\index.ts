import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';

const deleteStructuredData = async <
  ReqPayload,
  Data extends Partial<responses.getFolder> & {
    eventSchemaIds?: string[];
  } = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { folder } = data;
  if (!folder) {
    throw new ActionError('No folder provided');
  }

  try {
    for (const { sdo } of folder.contentTemplates) {
      if (data.eventSchemaIds?.includes(sdo.schemaId)) {
        await callGQL<responses.deleteStructuredData, ReqPayload, Data>(
          context,
          headers,
          queries.deleteStructuredData,
          {
            sdoId: sdo.id,
            schemaId: sdo.schemaId,
          }
        );
      } else {
        throw new ActionError(
          'SchemaId does not exist in list of known schemas'
        );
      }
    }
    return context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default deleteStructuredData;
