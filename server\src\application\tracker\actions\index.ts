import getMe from './getMe';
import getFile from './getFile';
import getEvent from './getEvent';
import getFolder from './getFolder';
import ingestFile from './ingestFile';
import searchFiles from './searchFiles';
import searchEvents from './searchEvents';
import createFolder from './createFolder';
import deleteFolder from './deleteFolder';
import updateFolder from './updateFolder';
import getMatchGroup from './getMatchGroup';
import getMatchGroups from './getMatchGroups';
import actionWrapper from '@util/actionWrapper';
import deleteTemporalData from './deleteTemporalData';
import createStructuredData from './createStructuredData';
import deleteStructuredData from './deleteStructuredData';
import updateStructuredData from './updateStructuredData';
import updateMatchGroupStructuredData from './updateMatchGroupStructuredData';
import getSignedWritableUrl from './getSignedWritableUrl';
import createContentTemplate from './createContentTemplate';
import deleteContentTemplate from './deleteContentTemplate';
import createFileTemporalData from './createFileTemporalData';
import deleteMatchGroupSearch from './deleteMatchGroupSearch';
import searchTracklets from './searchTracklets';
import deleteEvents from './deleteEvents';
import createMatchGroupStructuredData from './createMatchGroupStructuredData';
import getParentFolderId from './getParentFolderId';
import getTags from './getTags';
import getMatchGroupSelectedTracklets from './getMatchGroupSelectedTracklet';
import getBoundingBoxes from './getBoundingBoxes';
import createTimelineJob from './createTimelineJob';
import saveGeneratedTimeline from './saveGeneratedTimeline';
import deleteFiles from './deleteFiles';
import deleteTracklets from './deleteTracklets';
import deleteMatchGroups from './deleteMatchGroups';
import deleteGeneratedTimeline from './deleteGeneratedTimeline';
import getThumbnails from './getThumbnails';
import getRedisEvent from './getRedisEvent';
import setRedisEvent from './setRedisEvent';
import searchEventsByIds from './searchNewEvents';
import deleteMatchGroup from './deleteMatchGroup';
import searchTrackletsWithIntersectingBox from './searchTrackletsWithIntersectingBox';
import updateFile from './updateFile';
import updateLastSearchWithEngineId from './updateLastSearchWithEngineId';
import {
  checkCachedSchemaIdEvents,
  checkCachedSchemaIdMatchGroups,
} from './checkCachedSchemaIds';
import checkCreateRootFolder from './checkCreateRootFolder';
import updateRedisEvent from './updateRedisEvent';
import getAllEventSchemaIds from './getAllEventSchemaIds';
import importEvent from './importEvent';

export default {
  searchTrackletsWithIntersectingBox: actionWrapper(
    searchTrackletsWithIntersectingBox
  ),
  getMe: actionWrapper(getMe),
  getFile: actionWrapper(getFile),
  getEvent: actionWrapper(getEvent),
  getFolder: actionWrapper(getFolder),
  ingestFile: actionWrapper(ingestFile),
  searchFiles: actionWrapper(searchFiles),
  searchEvents: actionWrapper(searchEvents),
  searchEventsByIds: actionWrapper(searchEventsByIds),
  getTags: actionWrapper(getTags),
  createFolder: actionWrapper(createFolder),
  deleteFolder: actionWrapper(deleteFolder),
  updateFolder: actionWrapper(updateFolder),
  updateFile: actionWrapper(updateFile),
  getMatchGroup: actionWrapper(getMatchGroup),
  getMatchGroups: actionWrapper(getMatchGroups),
  getMatchGroupSelectedTracklets: actionWrapper(getMatchGroupSelectedTracklets),
  searchTracklets: actionWrapper(searchTracklets),
  deleteMatchGroupSearch: actionWrapper(deleteMatchGroupSearch),
  deleteTemporalData: actionWrapper(deleteTemporalData),
  createStructuredData: actionWrapper(createStructuredData),
  createMatchGroupStructuredData: actionWrapper(createMatchGroupStructuredData),
  deleteStructuredData: actionWrapper(deleteStructuredData),
  updateStructuredData: actionWrapper(updateStructuredData),
  updateMatchGroupStructuredData: actionWrapper(updateMatchGroupStructuredData),
  getSignedWritableUrl: actionWrapper(getSignedWritableUrl),
  createContentTemplate: actionWrapper(createContentTemplate),
  deleteContentTemplate: actionWrapper(deleteContentTemplate),
  createFileTemporalData: actionWrapper(createFileTemporalData),
  checkCachedSchemaIdEvents: actionWrapper(checkCachedSchemaIdEvents),
  checkCachedSchemaIdMatchGroups: actionWrapper(checkCachedSchemaIdMatchGroups),
  deleteEvents: actionWrapper(deleteEvents),
  getParentFolderId: actionWrapper(getParentFolderId),
  getBoundingBoxes: actionWrapper(getBoundingBoxes),
  createTimelineJob: actionWrapper(createTimelineJob),
  saveGeneratedTimeline: actionWrapper(saveGeneratedTimeline),
  deleteFiles: actionWrapper(deleteFiles),
  deleteTracklets: actionWrapper(deleteTracklets),
  deleteMatchGroups: actionWrapper(deleteMatchGroups),
  deleteMatchGroup: actionWrapper(deleteMatchGroup),
  deleteGeneratedTimeline: actionWrapper(deleteGeneratedTimeline),
  getThumbnails: actionWrapper(getThumbnails),
  getRedisEvent: actionWrapper(getRedisEvent),
  setRedisEvent: actionWrapper(setRedisEvent),
  updateRedisEvent: actionWrapper(updateRedisEvent),
  updateLastSearchWithEngineId: actionWrapper(updateLastSearchWithEngineId),
  checkCreateRootFolder: actionWrapper(checkCreateRootFolder),
  getAllEventSchemaIds: actionWrapper(getAllEventSchemaIds),
  importEvent: actionWrapper(importEvent),
};
