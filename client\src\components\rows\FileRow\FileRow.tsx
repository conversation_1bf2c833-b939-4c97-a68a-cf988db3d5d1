import cn from 'classnames';
import { Chip } from '@mui/material';
import { AlertLevel, createSnackNotification } from '@components/common';
import { RowProps } from '@components/common/Table/Table';
import { File } from '@shared-types/tracker';
import { constant } from 'lodash';
import './FileRow.scss';
import { toLocalTime } from '@utility/convert';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';

const FileRow = ({
  colData,
  rowData,
  rowIndex,
  selectedId,
  onRowClick,
  onDoubleRowClick,
  additionalProps,
}: RowProps<File, AdditionalProps>) => {
  const intl = useIntl();
  const { id } = rowData;
  const { isPendingDeletion } = additionalProps ?? {
    isPendingDeletion: constant(false),
  };

  const renderCell = ({ dataKey }: { dataKey: string }) => {
    switch (dataKey) {
      case 'fileName':
        return (
          <div role="cell" className="file-row__cell-fileName" data-testid="file-row-name">
            {rowData[dataKey]}
          </div>
        );
      case 'status': {
        const status = `${rowData[dataKey]}`;
        return (
          <div role="cell" className="file-row__cell-status">
            <Chip
              className={cn({
                'pending-deletion': isPendingDeletion(id),
                [rowData[dataKey]]: !isPendingDeletion(id),
                status
              })}
              // eslint-disable-next-line formatjs/enforce-default-message
              label={intl.formatMessage({ id: isPendingDeletion(id) ? 'pendingDeletion' : status }).toUpperCase()}
              variant="outlined"
            />
          </div>
        );
      }
      case 'uploadDate': {
        const uploadDate = `${rowData[dataKey]}`;
        return (
          <div role="cell" className="file-row__cell-uploadDate">
            {I18nTranslate.TranslateDate(toLocalTime(uploadDate))}
          </div>
        );
      }
    }
  };

  const fileRowOnClick = () => {
    if (isPendingDeletion(id)) {
      createSnackNotification(
        AlertLevel.Warning,
        'Warning',
        'This file is pending deletion and cannot be selected.'
      );
    } else {
      onRowClick?.(rowData);
    }
  };

  const fileRowOnDoubleClick = () => {
    if (isPendingDeletion(id)) {
      createSnackNotification(
        AlertLevel.Warning,
        'Warning',
        'This file is pending deletion and cannot be selected.'
      );
    } else {
      onDoubleRowClick?.(rowData);
    }
  };

  return (
    <div role="row" className={cn('file-row', { selected: selectedId === id })}>
      <div
        data-testid={`file-row-${rowData.id}`}
        className={cn('file-row__row')}
        onClick={fileRowOnClick}
        onDoubleClick={fileRowOnDoubleClick}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="file-row__cell"
            data-testid={`file-row-cell-${rowIndex}-${id}-${index}`}
            key={`FileRowCell-${id}-${rowIndex}-${id}-${index}`}
            style={{
              flexGrow: grow,
              width,
              minWidth: width ?? minWidth,
            }}
          >
            {renderCell({ dataKey })}
          </div>
        ))}
      </div>
    </div>
  );
};

interface AdditionalProps {
  isPendingDeletion: (id: string) => boolean;
}

export default FileRow;
