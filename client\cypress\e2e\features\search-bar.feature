Feature: Search Bar

  Background:
    Given The user is on Event Screen

  @e2e @search
  Scenario: Verify search result page when nothing matches
    When The user enters "!@$%^*()-=" into the search bar
    Then The user should see no results when the search yields no matches

  @e2e @search @skip
  Scenario: Search for an event and edit its name
    Given The user deletes the following Events if exist:
      | eventName                     |
      | e2e-search-edit-event         |
      | e2e-search-edit-event-updated |
    When The user creates default event name "e2e-search-edit-event"
    Then The user should see a success snackbar with message "e2e-search-edit-event"
    And The user clicks on cancel upload button
    Then The user should see "e2e-search-edit-event" in the event table
    When The user enters "e2e-search-edit-event" into the search bar
    # Note: Newly created events not appear in search results immediately after creation, even after waiting up to 30s
    Then The displayed event results should contain "e2e-search-edit-event"
    When The user selects the "event" named "e2e-search-edit-event"
    And The user clicks the "event" name located on the right side
    And The user changes "event" name to "e2e-search-edit-event-updated"
    Then The name of the "event" should be updated to "e2e-search-edit-event-updated"

  @e2e @search
  Scenario: Search for an existing event and view its details
    When The user enters "Cypress E2E Test" into the search bar
    Then The displayed event results should contain "Cypress E2E Test"
    When The user selects the "event" named "Cypress E2E Test"
    And The user clicks on "View Event"
    Then The user should navigate to event details page

  @e2e @search @skip
  Scenario: Search for an existing event and delete it
    When The user enters "e2e-search-edit-event-updated" into the search bar
    Then The displayed event results should contain "e2e-search-edit-event-updated"
    When The user selects the "event" named "e2e-search-edit-event-updated"
    And The user clicks on "Delete Event"
    And The user enters event name "e2e-search-edit-event-updated"
    Then The user verifies delete button is enabled and clicks it
    Then The user should see a success snackbar with message "Successfully deleted event e2e-search-edit-event-updated"

  @e2e @search @file
  Scenario: Search for a processed file and view its details
    When The user enters "4s testing tdo.mp4" into the search bar
    And The user navigates to the Files tab after searching
    Then The displayed file results should contain "4s testing tdo.mp4"
    When The user selects the "file" named "4s testing tdo.mp4"
    Then The user clicks the view file button in file detail
    Then The user should navigate to file details page

  @e2e @search @file
  Scenario: Verify user cannot open an unprocessed file
    When The user enters "People NN.mp4" into the search bar
    And The user navigates to the Files tab after searching
    Then The displayed file results should contain "People NN.mp4"
    When The user selects the "file" named "People NN.mp4"
    Then The view file button in file detail should be disabled

  @e2e @search @file @skip
  Scenario: Verify user can delete a file
