import getFolder from '../getFolder';
import { Context } from '../../../types';
import { ActionError } from '@common/errors';
import { responses } from '@tracker/graphQL';

const getEvent = async <ReqPayload, Data extends { eventSchemaIds: string[] }>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & Partial<responses.getEvent>>> => {
  const {
    data: { eventSchemaIds },
  } = context;
  const getFolderData = await getFolder(context);

  if (!getFolderData) {
    throw new ActionError('No folder found');
  }

  if (!eventSchemaIds || eventSchemaIds.length === 0) {
    throw new ActionError('No event schema IDs found');
  }

  const { data } = getFolderData;
  const { folder } = data;
  const event = folder?.contentTemplates.find((template) =>
    eventSchemaIds.includes(template.sdo.schemaId)
  );

  if (!event) {
    throw new ActionError(`No event sdo found for folder`);
  }

  const new_data = Object.assign({}, data, {
    event: event.sdo.data,
  });
  const new_context = Object.assign({}, context, { data: new_data });

  return new_context;
};

export default getEvent;
