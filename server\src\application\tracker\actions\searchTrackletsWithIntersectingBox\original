import { Context } from '@application/types';
import { ValidationError } from '@common/errors';
import { queries, responses } from '@tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { fingerprintSearch } from '../searchTracklets/elasticSearch';
import { SearchTrackletsWithIntersectingBoxResponse } from '../../../../../../types/responses';

const validDetectionType = ['person', 'vehicle'];


export interface SearchTrackletsWithIntersectingBoxData {
  searchData?: SearchTrackletsWithIntersectingBoxResponse;
};

const searchTrackletsWithIntersectingBox = async <
  ReqPayload,
  Data extends Partial<responses.getEvent> & Partial<responses.getFile> & SearchTrackletsWithIntersectingBoxData,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data>
> => {
  const { data, req, log } = context;
  const { pageSize, currentPage, type, xMin, xMax, yMin, yMax } = req.query;
  const { fileId } = req.params;
  const headers = { Authorization: req.headers.authorization };

  if (!type || !validDetectionType.includes(type as string)) {
    log.error("Missing or invalid query param 'type' (person | vehicle)");
    throw new ValidationError("Missing or invalid query param 'type' (person | vehicle)");
  }

  if (xMin === undefined || xMax === undefined || yMin === undefined || yMax === undefined ||
    Number.isNaN(parseInt(xMin as string)) || Number.isNaN(parseInt(xMax as string)) ||
    Number.isNaN(parseInt(yMin as string)) || Number.isNaN(parseInt(yMax as string))) {
    log.error('Missing or invalid box query param (xMin, xMax, yMin, yMax)');
    throw new ValidationError('Missing or invalid box query param (xMin, xMax, yMin, yMax)');
  }

  // Pull the engine id stored in the event, otherwise use the default.
  // Do not ever change the default engine GUID. This will break old events that do not store the engine id.
  const trackerEngineId = data?.event?.trackerEngineId ?? 'd77d6133-a801-472c-bc7e-48ddafec8590';

  const pgSize = Number(pageSize ?? 100);
  const currentPg = Number(currentPage ?? 1);
  const limit = pgSize;
  const offset = (currentPg - 1) * pgSize;

  const { searchMedia: results } = await callGQL<
    responses.IntersectingTrackletsAggregation,
    ReqPayload,
    Data
  >(context, headers, queries.searchMedia, {
    search: {
      index: [
        "mine"
      ],
      limit: 100,
      query: {
        operator: "and",
        conditions: [
          {
            operator: "term",
            field: "recordingId",
            value: fileId
          },
          {
            operator: "query_object",
            field: "object-recognition.series",
            query: {
              operator: "and",
              conditions: [
                // xMinA <= xMaxB && 
                {
                  operator: "range",
                  field: "boundingBox.minX",
                  lte: xMax
                },
                // xMaxA >= xMinB && 
                {
                  operator: "range",
                  field: "boundingBox.maxX",
                  gte: xMin
                },
                // yMinA <= yMaxB && 
                {
                  operator: "range",
                  field: "boundingBox.minY",
                  lte: yMax
                },
                // yMaxA >= yMinB
                {
                  operator: "range",
                  field: "boundingBox.maxY",
                  gte: yMin
                }
              ]
            }
          }
        ]
      },
      aggregate: [
        {
          field: "object-recognition.series.found",
          operator: "term",
          aggregate: [
            {
              field: "object-recognition.series.referenceId",
              operator: "term",
              distinct: true,
              limit: 10000,
              offset: 0
            }
          ]
        }
      ]
    }
  });

  const aggregationForType = results?.jsondata?.aggregations?.['object-recognition.series.found']?.['object-recognition.series.found']['object-recognition.series.found']?.buckets.find(t => t.key === type);

  let intersectingTracklets: string[] = [''];
  let totalResults = 0;
  if (aggregationForType) {
    intersectingTracklets = aggregationForType['object-recognition.series.referenceId'].buckets.map(b => b.key);
    totalResults = intersectingTracklets.length;
    intersectingTracklets = intersectingTracklets.slice(offset, offset + limit);
  }

  const { tracklets } = await fingerprintSearch({
    referenceTrackletIds: intersectingTracklets,
    limit: totalResults,
    offset: 0,
    context,
    headers,
    type: [type as string],
    times: true,
    trackerEngineId
  });

  data.searchData = {
    tracklets,
    type: type as string,
    fileId: fileId as string,
    currentPage: currentPg,
    pageSize: pgSize,
    totalCount: totalResults,
    totalPages: Math.ceil(totalResults / pgSize)
  }

  return context;
};

export default searchTrackletsWithIntersectingBox;
