import { AttributesJson, AttributeType } from '@shared-types/tracker';
import attributesJson from '@shared-assets/attributes.json';

export const getAttributesJson = (type: AttributeType): AttributesJson => {
  if (type === 'vehicle') {
    return attributesJson["d6ad2040-e2a0-40cc-bfc4-b3405daa7673_attr_vehicle"];
  } else {
    return attributesJson["d6ad2040-e2a0-40cc-bfc4-b3405daa7673_attr"];
  }
};
