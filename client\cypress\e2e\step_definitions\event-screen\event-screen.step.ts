import { When, Then, Given, Before } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';
import { ValidationButtonTypes } from '../../../support/helperFunction/activeTabHelper';
import '../activeTab/activeTab.step';

Before({ tags: '@event-screen' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/events\/\?sortBy/).as('fetchEvent');
});

Then('The {string} tab should be active', (activeTabName: string) => {
  mainPage.verifyTabIsActive(activeTabName);
});

When('The user clicks the {string} breadcrumb', (breadcrumbText: string) => {
  mainPage.clickBreadcrumb(breadcrumbText);
});

Then('The page should not navigate away', () => {
  mainPage.verifyPageNotNavigated();
});

When('The user enters wrong event name {string}', (wrongName: string) => {
  mainPage.enterEventNameToSearchInput(wrongName);
});

Then('The {string} button should still be disabled and textbox highlighted in red', (buttonText: string) => {
  if (buttonText === ValidationButtonTypes.DELETE) {
    mainPage.verifyDeleteButtonDisabledAndTextboxError();
  }
});

Given('A test event named {string} exists for deletion testing', (eventName: string) => {
  mainPage.createTestEvent(eventName);
  cy.waitMainPageIsLoaded();
});

When('The user navigates to the event deletion functionality for {string}', (eventName: string) => {
  mainPage.selectItem(eventName, 'event');
});

When('The user clicks on the Delete event button', () => {
  mainPage.clickDeleteEventButton();
});

Then('A confirmation dialog should appear with the message for {string}', (eventName: string) => {
  mainPage.verifyDeleteConfirmationDialog(eventName);
});

When('The user enters the event name {string} in the confirmation textbox', (eventName: string) => {
  mainPage.enterEventNameForDeletion(eventName);
});

When('The user clicks on the Delete button in the confirmation dialog', () => {
  mainPage.confirmEventDeletion();
});

Then('The event {string} should be successfully removed from the system', (eventName: string) => {
  mainPage.verifyEventDeleted(eventName);
});

When('The user clicks on new event button', () => {
  mainPage.clickNewEvent();
});

Then('The user enter {string} in the event name textbox', (eventName: string) => {
  mainPage.enterEventName(eventName);
});

Then('The user clicks on create event button', () => {
  mainPage.clickCreateEvent();
});

Then('The user should see {string} in the event table', (eventName: string) => {
  cy.awaitNetworkResponseCode({ alias: '@fetchEvent', code: 200 });
  mainPage.verifyEventInTable(eventName);
});

Then('The file should be uploaded successfully', () => {
  mainPage.verifyFileUploadedSuccessfully();
});

Then('The file {string} should appear in the upload area', (fileName: string) => {
  mainPage.verifyFileAppearsInUploadArea(fileName);
});

Then('The upload should complete {string}', (progress: string) => {
  mainPage.verifyUploadComplete(progress);
});
