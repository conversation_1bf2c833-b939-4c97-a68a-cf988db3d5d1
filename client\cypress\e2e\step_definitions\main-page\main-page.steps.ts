import { Before, Given, Then } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';

Before({ tags: '@landing-page' }, () => {
  cy.LoginLandingPage();
});

Given('The main page is loaded', () => {
  cy.waitMainPageIsLoaded();
});

Then('The user should see the correct column headers for Event Name and Event Time', () => {
  mainPage.verifyColumnHeaders();
});

Then('The user should see the application title Track', () => {
  mainPage.verifyAppTitle();
});
