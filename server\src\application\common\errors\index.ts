import ApiError from './ApiError';
import ActionError from './ActionError';
import GraphQLError from './GraphQLError';
import DatabaseError from './DatabaseError';
import ForbiddenError from './ForbiddenError';
import ValidationError from './ValidationError';
import UnauthorizedError from './UnauthorizedError';
import InternalServerError from './InternalServerError';
import NotFoundError from './NotFoundError';
import ActionValidationError from './ActionValidationError';

const errorCodeMap = [
  { errorName: 'EventAlreadyCreatedForFolder', errorCode: 204 },
  { errorName: 'AppNotEnabledForUser', errorCode: 401 },
  { errorName: 'ValidationError', errorCode: 400 },
  { errorName: 'UnauthorizedError', errorCode: 401 },
  { errorName: 'ForbiddenError', errorCode: 403 },
  { errorName: 'NotFoundError', errorCode: 404 },
  { errorName: 'ActionError', errorCode: 500 },
  { errorName: 'ActionValidationError', errorCode: 500 },
  { errorName: 'GraphQLError', errorCode: 500 },
  { errorName: 'DatabaseError', errorCode: 500 },
  { errorName: 'InternalServerError', errorCode: 500 },
  { errorName: 'ApiError', errorCode: 500 },
];

export const errorCode = (errorName: string) =>
  errorCodeMap.find((error) => error.errorName === errorName)?.errorCode || 500;

export {
  ApiError,
  ActionError,
  GraphQLError,
  DatabaseError,
  ForbiddenError,
  ValidationError,
  UnauthorizedError,
  InternalServerError,
  NotFoundError,
  ActionValidationError,
};

export default {
  ApiError,
  ActionError,
  GraphQLError,
  DatabaseError,
  ForbiddenError,
  ValidationError,
  UnauthorizedError,
  InternalServerError,
  NotFoundError,
  ActionValidationError,
};
