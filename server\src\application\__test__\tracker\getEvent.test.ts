import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.fetchAllSchemaIds)) {
        return Promise.resolve({
          dataRegistry: {
            schemas: {
              records: [
                {
                  id: 'schemaId',
                },
                {
                  id: 'schema',
                },
                {
                  id: 'id',
                },
                {
                  id: 'eventId',
                },
                {
                  id: 'an-event-id',
                },
                {
                  id: 'sdoId',
                },
              ],
            },
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'an-event-id',
            contentTemplates: [
              {
                id: 'id',
                sdo: {
                  id: 'sdoId',
                  schemaId: 'schemaId',
                  data: {
                    id: 'eventId',
                    name: 'oldName',
                    tags: ['oldTag'],
                    createdBy: 'CreatedBy',
                    createdByName: 'CreatedByName',
                    description: 'Description',
                    eventStartDate: 'EventStartDate',
                    eventEndDate: 'EventEndDate',
                    trackerEngineId: env.trackerEngineId,
                  },
                },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
    }
  ),
}));

describe('get event', () => {
  it('get event', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/event/an-event-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getFolder,
      {
        folderId: 'an-event-id',
      }
    );
  });

  it('cannot find event w/o eventId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/event')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
