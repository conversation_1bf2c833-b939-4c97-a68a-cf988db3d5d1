import env from '../../../../env';
import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';

export interface SchemasResponse {
  schemas: {
    records: {
      id: string;
    }[];
  };
}

export const checkCachedSchemaIdEvents = async <
  ReqPaylod,
  Data extends object = object,
>(
  context: Context<ReqPaylod, Data>
) => await checkCachedSchemaIds<ReqPaylod, Data>(context, 'eventsRegistryId');

export const checkCachedSchemaIdMatchGroups = async <
  ReqPaylod,
  Data extends object = object,
>(
  context: Context<ReqPaylod, Data>
) =>
  await checkCachedSchemaIds<ReqPaylod, Data>(context, 'matchGroupsRegistryId');

const checkCachedSchemaIds = async <ReqPaylod, Data extends object = object>(
  context: Context<ReqPaylod, Data>,
  registryId: keyof typeof env.registryIds
) => {
  const { cache, data: _data, req, log: _log } = context;
  const headers = { Authorization: req.headers.authorization };
  const schemaId = registryId.replace('Registry', 'Schema');
  const params = req.params;
  if (cache.get(schemaId)) {
    return context;
  }

  let dataRegistry: responses.lookupLatestSchemaId['dataRegistry'];
  let schemaIdByName = '';
  try {
    try {
      const resp = await callGQL<
        responses.lookupLatestSchemaId,
        ReqPaylod,
        Data
      >(context, headers, queries.lookupLatestSchemaId, {
        registryId: env.registryIds[registryId],
      });
      dataRegistry = resp.dataRegistry;
    } catch (e) {
      if (e.message.includes('The requested object was not found')) {
        const resp = await callGQL<SchemasResponse, ReqPaylod, Data>(
          context,
          headers,
          queries.getSchemaIdsByDataRegistryName(
            registryId === 'eventsRegistryId'
              ? 'track-event'
              : 'track-match-group'
          )
        );

        schemaIdByName = resp.schemas?.records?.[0]?.id;

        if (!schemaIdByName || schemaIdByName.length === 0) {
          throw new ActionError('Could not retrieve data registry schema');
        }
      }
    }

    if (dataRegistry?.publishedSchema?.id) {
      cache.set(schemaId, dataRegistry.publishedSchema.id);
    } else if (schemaIdByName) {
      cache.set(schemaId, schemaIdByName);
    } else {
      throw new ActionError('No registry schema to set');
    }
    const new_req = Object.assign({}, req, {
      headers: req.headers,
      params: params,
    });
    const new_context = Object.assign({}, context, { req: new_req });
    return new_context;
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
};
