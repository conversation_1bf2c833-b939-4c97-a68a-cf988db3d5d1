import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import deleteContentTemplate from '../deleteContentTemplate';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import RedisWrapper from '../../../../redisWrapper';
import Redis from 'ioredis-mock';

let cxt: Context<object, responses.getFolder & { eventSchemaIds: string[] }>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

describe('Delete Content Template', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        eventSchemaIds: ['id'],
        folder: {
          contentTemplates: [
            {
              id: 'id',
              sdo: {
                data: {
                  id: 'id',
                  name: '',
                  tags: [],
                  createdBy: '',
                  createdByName: '',
                  description: '',
                  eventStartDate: '',
                  eventEndDate: '',
                  createdDateTime: '',
                  modifiedDateTime: '',
                  matchGroupsCount: 0,
                  filesCount: 0,
                },
                id: 'id',
                schemaId: 'id',
              },
            },
          ],
          id: '',
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      // @ts-expect-error TODO: Why are we passing unassigend clientRedis?
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Successfully deletes a content template', async () => {
    const response = await deleteContentTemplate(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { contentTemplateId: 'id' }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no folder', async () => {
    // @ts-expect-error TODO: Does this make sense with types?
    cxt.data.folder = undefined;

    expect(async () => await deleteContentTemplate(cxt)).rejects.toThrowError(
      'No folder provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
