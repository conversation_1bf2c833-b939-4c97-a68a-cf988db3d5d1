import {
  Event,
  File,
  MatchGroup,
  RootFolder,
  TemporalData,
  Tracklet,
  User,
} from '../../../../../types/tracker';

export interface deleteFolderContentTemplate {
  deleteFolderContentTemplate: {
    id: string;
    message: string;
  };
}

export interface deleteStructuredData {
  deleteStructuredData: {
    id: string;
    message: string;
  };
}

export interface deleteFolder {
  deleteFolder: {
    id: string;
    message: string;
  };
}

export interface me {
  me: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    organizationId: string;
    acls: {
      objectId: string;
      objectType: string;
      access: {
        owner: boolean;
      };
    };
  };
}

export interface deleteTemporalData {
  deleteTDO: {
    id: string;
    message: string;
  };
}
export interface deleteMatchGroupSearch {
  deleteTDO: {
    id: string;
    message: string;
  };
}

export interface getFolder {
  folder: {
    id: string;
    name: string;
    description: string;
    createdDateTime: string;
    modifiedDateTime: string;
    parent: {
      organization: {
        id: string;
      };
    };
    contentTemplates: Array<{
      id: string;
      sdo: {
        id: string;
        schemaId: string;
        data: Event;
      };
    }>;
    treeObjectId?: string;
  };
}

export interface getFileTemporalData {
  temporalDataObject: TemporalData;
}

export interface createFolder {
  createFolder: {
    id: string;
    name: string;
    description: string;
    ownerId: string;
  };
}

export interface dataRegistry {
  dataRegistry: {
    schemas: {
      records: {
        id: string;
      }[];
    };
  };
}

export interface createStructuredData {
  createStructuredData: {
    id: string;
    data: Event;
    schemaId: string;
    createdDateTime: string;
    modifiedDateTime: string;
  };
}

export interface structuredMatchGroupData {
  createStructuredData: {
    id: string;
    data: MatchGroup;
    schemaId: string;
    createdDateTime: string;
    modifiedDateTime: string;
  };
}

export interface getGeneratedTimelineDetails {
  temporalDataObject: TemporalData;
}

export interface getUsers {
  [index: string]: Error[] | User;
  errors: Error[];
}

export interface createFolderContentTemplate {
  createFolderContentTemplate: {
    id: string;
    createdDateTime: string;
    modifiedDateTime: string;
  };
}

export interface getSignedWritableUrl {
  getSignedWritableUrl: {
    url: string;
    getUrl: string;
    unsignedUrl: string;
    key: string;
    bucket: string;
    expiresInSeconds: number;
  };
}

export interface createTemporalData {
  createTDO: {
    id: string;
    name: string;
    status: string;
    details: unknown;
    createdDateTime: string;
    modifiedDateTime: string;
    createdBy: string;
    primaryAsset: {
      signedUri: string;
    };
    streams: {
      protocol: string;
      uri: string;
    }[];
  };
}

export interface searchMedia<R = unknown> {
  searchMedia: {
    jsondata: {
      results: Array<R>;
      totalResults: {
        value: number;
        relation: string;
      };
      limit: number;
      from: number;
      to: number;
      timestamp: number;
    };
  };
}

export interface lookupLatestSchemaId {
  dataRegistry?: {
    publishedSchema?: {
      id?: string;
    };
  };
}

export interface createJob {
  createJob: {
    id: string;
    targetId: string;
    status: string;
  };
}

export interface createSpliceJob {
  spliceJob: {
    tdoId: string;
    jobId: string;
    status: string;
  };
}

export interface searchStructuredDataObjects<D = object> {
  structuredDataObjects: {
    records: {
      data: D;
      createdDateTime: string;
      modifiedDateTime: string;
    }[];
    count: number;
    offset: number;
    limit: number;
    orderBy: {
      field: 'createdDateTime' | 'modifiedDateTime';
      direction: 'asc' | 'desc';
    }[];
  };
}

export interface searchStructuredDataObject<D = object> {
  structuredDataObject: {
    id: string;
    data: D;
    createdDateTime: string;
    modifiedDateTime: string;
  };
}

export interface updateFolder {
  updateFolder: {
    id: string;
    name: string;
  };
}

export interface getEvent extends getFolder {
  event: Event;
}

export type updateStructuredData = createStructuredData & getEvent;

export interface getMatchGroups {
  matchGroups: {
    pagination: {
      pageSize: number;
      currentPage: number;
      totalPages: number;
      totalCount: number;
    };
    sort: {
      field: string;
      direction: string;
    };
    eventId: string;
    searchResults: Array<MatchGroup>;
  };
}

export interface getMatchGroupSelectedTracklets {
  selectedTracklets: {
    results?: Tracklet[];
    matchGroupId: string;
    matchGroupName: string;
    eventId: string;
  };
}

export interface searchMatchTracklets {
  matchGroupSearch: {
    results: Tracklet[];
    type?: 'person' | 'vehicle';
    referenceTrackletId: string;
    searchId: string;
    searchName: string;
    searchTime: string;
    matchGroupId: string;
    matchGroupName: string;
    eventId: string;
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    tdoIds: string[];
    fileNames: string[];
    allTdos: { tdoId: string; fileName: string }[];
  };
}

export interface searchFileTracklets {
  fileSearch: {
    results: Tracklet[];
    type?: string;
    referenceTrackletId: string;
    searchId: string;
    searchName: string;
    searchTime: string;
    matchGroupId: string;
    matchGroupName: string;
    eventId: string;
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
  };
}

export interface getMatchGroup {
  matchGroup: MatchGroup;
  matchGroupId: string;
}

export interface searchFiles {
  searchFiles: {
    searchResults: Array<File>;
    pageSize: number;
    currentPage: number;
    totalCount: number;
    totalPages: number;
  };
}

export interface getTags {
  getTags: {
    tags: Array<string>;
  };
}

export interface searchEvents {
  eventId: string;
  searchEvents: {
    searchResults: Array<Event>;
    pageSize: number;
    currentPage: number;
    totalCount: number;
    totalPages: number;
  };
}

export interface getFile {
  file: File;
}

export interface getMe {
  userId: string;
  firstName: string;
  lastName: string;
  userEmail: string;
  userOrganizationId: string;
}

export interface createFileTemporalData {
  temporalData: TemporalData;
}

export interface folderTDOs {
  folder: {
    id: string;
    childTDOs: {
      records: {
        id: string;
        name: string;
      }[];
    };
  };
}

export interface getRootFolder {
  rootFolders: RootFolder[];
}

export interface createRootFolder {
  createRootFolders: RootFolder[];
}

export interface getParentFolderId {
  parentFolderId: string;
}

export interface FingerprintSearchResult {
  organizationId: string;
  recordingId: string;
  assetId: string;
  referenceId: string;
  fingerprintVector: number[];
  sourceEngineId: string;
  tags: { key: string; value: string }[];
  startTimeMs: number;
  stopTimeMs: number;
  label: string;
  id: string;
  score?: number;
  vendor?: {
    startTimeMs?: number;
    stopTimeMs?: number;
  };
}

export interface fingerprintSearch {
  searchMedia: {
    jsondata: {
      results: FingerprintSearchResult[];
      totalResults: number;
      limit: number;
      from: number;
      to: number;
    };
  };
}

// export interface aggregateFingerprintSearch {
//   searchMedia: {
//     jsondata: {
//       results: { referenceId: string; id: string; score: number }[];
//       totalResults: number;
//       limit: number;
//       from: number;
//       to: number;
//     };
//   };
// }

export interface aggregateFingerprintSearch {
  searchMedia: {
    jsondata: {
      aggregations: {
        recordingId: {
          doc_count_error_upper_bound: number;
          sum_other_doc_count: number;
          buckets: {
            key: string;
            doc_count: number;
          }[];
        };
      };
    };
  };
}

export interface IntersectingTrackletsAggregation {
  searchMedia: {
    jsondata: {
      aggregations: {
        prefilter: AggregationsPrefilter;
      };
    };
  };
}

export interface AggregationsPrefilter {
  prefilter: PurplePrefilter;
}

export interface PurplePrefilter {
  doc_count: number;
  prefilter: FluffyPrefilter;
}

export interface FluffyPrefilter {
  buckets: Buckets;
}

export interface Buckets {
  prefilter: BucketsPrefilter;
}

export interface BucketsPrefilter {
  doc_count: number;
  'object-recognition.series.found': ObjectRecognitionSeriesFound;
}

export interface ObjectRecognitionSeriesFound {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: ObjectRecognitionSeriesFoundBucket[];
}

export interface ObjectRecognitionSeriesFoundBucket {
  key: string;
  doc_count: number;
  referenceIds: ReferenceIDS;
  TotalCount: TotalCount;
}

export interface TotalCount {
  value: number;
}

export interface ReferenceIDS {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: ReferenceIDSBucket[];
}

export interface ReferenceIDSBucket {
  key: string;
  doc_count: number;
  sort_aggregation: TotalCount;
}

export interface FilenamesByTdoIds {
  temporalDataObjects: {
    count: number;
    records: {
      id: string;
      name: string;
    }[];
  };
}

export interface FolderByTdoIds {
  [key: string]: {
    eventId: string;
    eventName: string;
  };
}

export interface trackerEngineResultObject {
  fingerprintVector: number[];
  label: string;
  referenceId: string;
  tags: { key: string; value?: string }[];
  vendor: {
    startTimeMs: number;
    stopTimeMs: number;
    uri?: string;
  };
}

export interface trackerEngineResultSeries {
  object: {
    boundingPoly: { x: number; y: number }[];
    referenceId: string;
    type: string;
    label: string;
  };
  startTimeMs: number;
  stopTimeMs: number;
}

export interface trackerEngineResultJsonData {
  object: trackerEngineResultObject[];
  series: trackerEngineResultSeries[];
}

export interface engineResult<R = unknown> {
  tdoId: string;
  engineId: string;
  jsondata: R;
}

export interface engineResults<R = unknown> {
  engineResults: {
    records: engineResult<R>[];
  };
}

export interface getThumbnails {
  thumbnails: Record<
    string,
    {
      thumbnailUrls: { best: string };
      expiresDateTime: string;
    }
  >;
}
