import { useMemo, useEffect, useRef, useState } from 'react';
import './FileViewer.scss';
import { range, throttle } from 'lodash';
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from '@mui/material/AccordionSummary';
import { useSelector } from 'react-redux';
import { useAppDispatch } from '@store/hooks';
import { PlayerReference } from 'video-react';
import {
  Breadcrumbs,
  Tracklet as TrackletComp,
  TrackletLoading,
  DetectedAttributes,
} from '@components/common';
import { BoundingPoly, Tracklet } from '@shared-types/tracker';
import { MediaPlayer } from '@veritone/glc-react';
import { useNavigate, useParams } from 'react-router-dom';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Pagination from '@components/common/Pagination/Pagination';
import ExpandMoreSharpIcon from '@mui/icons-material/ExpandMoreSharp';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import FileMetadata from '@components/common/FileMetadata/FileMetadata';
import ThumbnailScaler from '@components/common/ThumbnailScaler/ThumbnailScaler';
import {
  Box,
  Button,
  Divider,
  Skeleton,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  styled,
} from '@mui/material';
import FindAndFilterMatches from '@components/FindAndFilterMatches/FindAndFilterMatches';
import {
  getBoundingBoxes,
  getFile,
  getFileById,
  getFileResultsById,
  selectBoundingBoxes,
  selectFile,
  selectFileResults,
  setFileResultsPagination,
  setSelectedTracklet,
  selectSelectedTracklet,
  getEvent,
  selectEvent,
  selectSelectedTrackletFile,
  getThumbnails,
  selectThumbnails,
  updateFileById,
  getFileResultsByRegion,
  updateNumberOfSourceBoundingBoxes,
  selectNumberOfSourceBoundingBoxes,
  setSelectedAttributes,
  selectEnableTrackletsIntersection,
  setEnableTrackletsIntersection,
} from '@store/modules/file/slice';
import { CurrentTab, TrackletType } from '@store/modules/file/types';
import { frameAlignStartTimeMs, frameAlignStopTimeMs } from '@utility/frames';
import ls from 'localstorage-slim';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import EditNoteOutlinedIcon from '@mui/icons-material/EditNoteOutlined';
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import {
  deleteBoundingBoxLocalStorage,
  getBoundingBoxLocalStorage,
  setBoundingBoxLocalStorage,
} from '@utility/localStorage';
import { AsyncThunkAction } from '@reduxjs/toolkit';
import { GetFileSearchResultsResponse, SearchTrackletsWithIntersectingBoxResponse } from '@shared-types/responses';
import { RootState } from '@store/store';
import HttpClient from '@store/dependencies/httpClient';

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme: _theme }) => ({
  '&::before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={
      <ExpandMoreSharpIcon sx={{ color: '#212121', fontSize: '1.5rem' }} />
    }
    {...props}
  />
))(({ theme }) => ({
  flexDirection: 'row',
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(180deg)',
  },
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const inputStyle = {
  '& .MuiOutlinedInput-root': {
    height: '32px',
    marginRight: '4px',
    marginTop: '4px',
    borderRadius: '6px',
  },
};

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  paddingTop: 0,
  paddingLeft: theme.spacing(2),
  paddingBottom: theme.spacing(2),
}));

const typeValueMap: { [key: string]: TrackletType } = {
  person: 'person',
  people: 'person',
  vehicles: 'vehicle',
  vehicle: 'vehicle',
};

/* eslint-disable react-hooks/exhaustive-deps */

const FileViewer = () => {
  const intl = useIntl();
  const { eventId, fileId } = useParams();
  const [expanded, setExpanded] = useState<
    '' | 'attributes' | 'file-meta' | 'ai-engines'
  >('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTab, setCurrentTab] = useState<CurrentTab>('people');
  const playerRef = useRef<PlayerReference | null>(null);
  const [thumbnailScale, setThumbnailScale] = useState(
    Number(ls.get('thumbnailScale') ?? 100)
  );
  const [isEditing, setIsEditing] = useState(false);
  const [editableFileName, setEditableFileName] = useState('');
  const [resetOverlayTrigger, setResetOverlayTrigger] = useState(false);
  const initialBoundingBoxPositions = fileId
    ? getBoundingBoxLocalStorage(fileId).map((boundingBoxLocalStorage) => ({
        id: boundingBoxLocalStorage.value.id,
        boundingPoly: boundingBoxLocalStorage.value.boundingPoly,
      }))
    : [];

  // Autoplay logic
  const autoplayStatusRef = useRef<
    '' | 'Seeking' | 'Seeked' | 'Playing' | 'Done'
  >('');
  const autoplayStopRef = useRef(0);

  const setDefaultAutoplay = () => {
    autoplayStatusRef.current = '';
    autoplayStopRef.current = 0;
  };

  playerRef.current?.subscribeToStateChange((state) => {
    setIsPlaying(!state.paused);

    // 1.) Track when seeking
    if (autoplayStatusRef.current === 'Seeking' && !state.seeking) {
      autoplayStatusRef.current = 'Seeked';
    }
    // 2.) Track when seeked
    else if (autoplayStatusRef.current === 'Seeked' && !state.paused) {
      autoplayStatusRef.current = 'Playing';
      playerRef.current?.play();
    } else if (autoplayStatusRef.current === 'Seeked' && state.paused) {
      playerRef.current?.pause();
    }
    // 3.) Track while playing
    else if (autoplayStatusRef.current === 'Playing' && !state.paused) {
      const alignedStopTimeMs = frameAlignStopTimeMs(
        autoplayStopRef.current,
        frameRate
      );
      const frameAlignCurrentTimeMs = frameAlignStartTimeMs(
        state.currentTime * 1000,
        frameRate
      );
      if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
        autoplayStatusRef.current = 'Done';
        playerRef.current?.pause();
      }
    } else if (
      autoplayStatusRef.current === 'Playing' &&
      state.userActivity &&
      state.paused
    ) {
      setDefaultAutoplay();
    }
    // 4.) Track when done playing
    else if (autoplayStatusRef.current === 'Done') {
      setDefaultAutoplay();
    }
  });

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const file = useSelector(selectFile);
  const event = useSelector(selectEvent);
  const fileResults = useSelector(selectFileResults);
  const boundingBoxes = useSelector(selectBoundingBoxes);
  const selectedTracklet = useSelector(selectSelectedTracklet);
  const selectedTrackletFile = useSelector(selectSelectedTrackletFile);
  const thumbnails = useSelector(selectThumbnails);
  const numberOfSourceBoundingBoxes = useSelector(
    selectNumberOfSourceBoundingBoxes
  );
  const enableTrackletsIntersection = useSelector(
    selectEnableTrackletsIntersection
  );

  const streams = file.streams;
  const thumbnailAssets = file.thumbnailAssets;
  const frameRate = file.frameRate;

  const breadcrumbLoading =
    event.apiStatus === 'loading' || file.apiStatus === 'loading';
  const fileResultsLoading =
    currentTab === 'people'
      ? fileResults.person.apiStatus === 'loading'
      : fileResults.vehicle.apiStatus === 'loading';
  const fileLoading =
    currentTab === 'people'
      ? selectedTrackletFile.person.apiStatus === 'loading'
      : selectedTrackletFile.vehicle.apiStatus === 'loading';
  const currentFile =
    currentTab === 'people'
      ? selectedTrackletFile.person.file
      : selectedTrackletFile.vehicle.file;
  const noFile = !currentFile && !fileLoading;
  const hasFile = currentFile && !fileLoading;
  const trackletType: TrackletType =
    currentTab === 'vehicles' ? ('vehicle' as const) : ('person' as const);
  const fileResultsDispatchPromiseRef = useRef<DispatchPromise | null>(null);

  const actionMenuItems: ActionMenuItem[] = [
    {
      label: intl.formatMessage({ id: 'filterTrackletsByRegion', defaultMessage: 'Show detections in selected region' }),
      onClick: ({
        id: _id,
        boundingPoly,
      }: {
        id: string;
        boundingPoly: BoundingPoly;
      }) => {
        dispatch(
          setSelectedAttributes({
            person: {},
            vehicle: {},
          })
        );
        dispatchGetFileResults(
          getFileResultsByRegion({
            fileId: fileId || '',
            eventId: eventId || '',
            trackletType,
            sourceBoundingBox: boundingPoly,
            page: 1,
            limit: 100,
          })
        );
      },
    },
    {
      label: intl.formatMessage({ id: 'removeBoundingBox', defaultMessage: 'Remove' }),
      onClick: ({
        id: _id,
        boundingPoly: _boundingPoly,
      }: {
        id: string;
        boundingPoly: BoundingPoly;
      }) => {
        dispatch(
          updateNumberOfSourceBoundingBoxes(numberOfSourceBoundingBoxes - 1)
        );
      },
      isDelete: true,
    },
  ];

  useEffect(() => {
    const missingTracklets = fileResults[trackletType].results.reduce<
      Array<{ trackletId: string; orgId: string; fileId: string }>
    >((arr, { trackletId, orgId, fileId }) => {
      const thumbnailInCache = thumbnails && trackletId in thumbnails;
      const isExpired =
        thumbnailInCache &&
        new Date(thumbnails[trackletId].expiresDateTime) <= new Date();

      if (!thumbnailInCache || isExpired) {
        arr.push({ trackletId, orgId, fileId });
      }
      return arr;
    }, []);

    if (missingTracklets.length) {
      dispatch(getThumbnails(missingTracklets));
    }
  }, [fileResults, trackletType]);

  useEffect(() => {
    if (!eventId) {
      return navigate(`/`, { replace: true });
    }
    if (!fileId) {
      return navigate(`/event/${eventId}`, { replace: true });
    }

    dispatch(getEvent({ eventId }));
    dispatch(getFileById({ fileId }));
  }, [eventId, fileId]);

  useEffect(() => {
    // Update enableTrackletsIntersection flag and number of bounding boxes
    // only when the event is loaded
    if (event.data) {
      const enableTrackletsIntersection = Boolean(
        event.data.trackerEngineId &&
          event.data.trackerEngineId !==
            'd77d6133-a801-472c-bc7e-48ddafec8590' &&
          (window?.config?.enableTrackletsIntersection ?? true)
      );

      dispatch(
        setEnableTrackletsIntersection(enableTrackletsIntersection)
      );

      if (fileId) {
        const numberOfSourceBoundingBoxes = enableTrackletsIntersection
          ? getBoundingBoxLocalStorage(fileId).length
          : 0;

        dispatch(
          updateNumberOfSourceBoundingBoxes(numberOfSourceBoundingBoxes)
        );
      }
    }
  }, [event.data, fileId]);

  useEffect(() => {
    if (
      initialBoundingBoxPositions &&
      initialBoundingBoxPositions?.[0]?.boundingPoly
    ) {
      dispatchGetFileResults(
        getFileResultsByRegion({
          fileId: fileId || '',
          eventId: eventId || '',
          trackletType,
          sourceBoundingBox: initialBoundingBoxPositions[0].boundingPoly,
          page: 1,
          limit: 100,
        })
      );
    } else if (eventId && fileId) {
      dispatchGetFileResults(
        getFileResultsById({
          fileId,
          eventId,
          trackletType,
          page: 1,
          limit: 100,
        })
      );
    }
  }, [eventId, fileId, trackletType]);

  useEffect(() => {
    if (selectedTracklet?.[trackletType]?.trackletId) {
      dispatch(
        getBoundingBoxes({
          trackletId: selectedTracklet[trackletType]?.trackletId,
          type: trackletType,
        })
      );
    }
  }, [selectedTracklet, trackletType]);

  useEffect(() => {
    if (
      boundingBoxes.length &&
      boundingBoxes[0].trackletId === selectedTracklet.person?.trackletId
    ) {
      autoplayStopRef.current = selectedTracklet.person.stopTimeMs;
      autoplayStatusRef.current = 'Seeking';
      const seekTime =
        Math.ceil((selectedTracklet.person.startTimeMs / 1000) * frameRate) /
        frameRate;
      playerRef.current?.seek(seekTime);
    }
    if (
      boundingBoxes.length &&
      boundingBoxes[0].trackletId === selectedTracklet.vehicle?.trackletId
    ) {
      autoplayStopRef.current = selectedTracklet.vehicle.stopTimeMs;
      autoplayStatusRef.current = 'Seeking';
      const seekTime =
        Math.ceil((selectedTracklet.vehicle.startTimeMs / 1000) * frameRate) /
        frameRate;
      playerRef.current?.seek(seekTime);
    }
  }, [boundingBoxes, frameRate, selectedTracklet]);

  useEffect(() => {
    if (file.apiStatus === 'failure') {
      return navigate(`/event/${eventId}`, { replace: true });
    }
  }, [file]);

  useEffect(() => {
    if (selectedTracklet.vehicle) {
      dispatch(getFile({ tracklet: selectedTracklet.vehicle }));
    }
  }, [selectedTracklet.vehicle]);

  useEffect(() => {
    if (selectedTracklet.person) {
      dispatch(getFile({ tracklet: selectedTracklet.person }));
    }
  }, [selectedTracklet.person]);

  useEffect(() => {
    if (!fileLoading && (selectedTracklet.person || selectedTracklet.vehicle)) {
      setTimeout(() => setExpanded('attributes'), 100);
    } else {
      setExpanded('');
    }
  }, [fileLoading]);

  const dispatchGetFileResults = (
    action: AsyncThunkAction<
      GetFileSearchResultsResponse | SearchTrackletsWithIntersectingBoxResponse, // Return type
      {
        trackletType: TrackletType;
        fileId: string;
        eventId: string;
        sourceBoundingBox?: BoundingPoly;
        page: number;
        limit: number;
      }, // Argument type
      {
        getState: () => RootState;
        extra: { http: HttpClient };
      } // Thunk API configuration type
    >
  ) => {
    // Cancel previous dispatch
    if (fileResultsDispatchPromiseRef.current) {
      fileResultsDispatchPromiseRef.current.abort();
    }
    const dispatchPromise = dispatch(action);
    fileResultsDispatchPromiseRef.current = dispatchPromise;
  };

  const handleDetailAccordionChange =
    (panel: 'attributes' | 'file-meta' | 'ai-engines') =>
    (_event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : '');
    };

  const handleTabChange = (
    _: React.SyntheticEvent,
    newValue: 'people' | 'vehicles'
  ) => {
    setCurrentTab(newValue);
  };

  const handleTrackletClick = (tracklet: Tracklet) => {
    dispatch(setSelectedTracklet({ tracklet }));
    setDefaultAutoplay();
  };

  const changePage =
    (trackletType: TrackletType, pageOffset?: number, setPage?: number) =>
    () => {
      const { currentPage, pageSize } =
        trackletType === 'person' ? fileResults.person : fileResults.vehicle;
      dispatch(
        setFileResultsPagination({
          trackletType,
          pageSize,
          currentPage: setPage ? setPage : currentPage + (pageOffset ?? 0),
        })
      );

      console.log(
        'change page',
        initialBoundingBoxPositions?.[0]?.boundingPoly
      );

      if (initialBoundingBoxPositions?.[0]?.boundingPoly) {
        dispatchGetFileResults(
          getFileResultsByRegion({
            fileId: fileId || '',
            eventId: eventId || '',
            trackletType,
            sourceBoundingBox: initialBoundingBoxPositions[0].boundingPoly,
            page: setPage ? setPage : currentPage + (pageOffset ?? 0),
            limit: pageSize,
          })
        );
      } else {
        dispatchGetFileResults(
          getFileResultsById({
            fileId: fileId || '',
            eventId: eventId || '',
            trackletType: typeValueMap[currentTab],
            page: setPage ? setPage : currentPage + (pageOffset ?? 0),
            limit: pageSize,
          })
        );
      }
    };

  const setPageSize = (trackletType: TrackletType) => (pageSize: number) => {
    dispatch(
      setFileResultsPagination({ trackletType, pageSize, currentPage: 1 })
    );

    if (initialBoundingBoxPositions?.[0]?.boundingPoly) {
      dispatchGetFileResults(
        getFileResultsByRegion({
          fileId: fileId || '',
          eventId: eventId || '',
          trackletType,
          sourceBoundingBox: initialBoundingBoxPositions[0].boundingPoly,
          page: 1,
          limit: pageSize,
        })
      );
    } else {
      dispatchGetFileResults(
        getFileResultsById({
          fileId: fileId || '',
          eventId: eventId || '',
          trackletType,
          page: 1,
          limit: pageSize,
        })
      );
    }
  };

  const handleAttributeChange = () => {
    dispatchGetFileResults(
      getFileResultsById({
        fileId: fileId || '',
        eventId: eventId || '',
        trackletType,
        page: 1,
        limit: 100,
      })
    );
  };

  const onAttributesChange = useMemo(
    () =>
      throttle(handleAttributeChange, 2000, {
        leading: false,
        trailing: true,
      }),
    [dispatch, trackletType]
  );

  const onClearAttributes = () => {
    dispatchGetFileResults(
      getFileResultsById({
        fileId: fileId || '',
        eventId: eventId || '',
        trackletType,
        page: 1,
        limit: 100,
      })
    );
  };

  return (
    <div className="file-viewer" data-testid="file-viewer">
      <div className="file-viewer__header">
        <Breadcrumbs
          loading={breadcrumbLoading}
          event={event.data}
          file={file}
        />
        <FindAndFilterMatches
          isFileViewer
          attributeFilter
          trackletType={trackletType}
          clearAll
          selectedTracklets={
            currentTab === 'people'
              ? selectedTracklet.person
                ? [selectedTracklet.person]
                : undefined
              : selectedTracklet.vehicle
              ? [selectedTracklet.vehicle]
              : undefined
          }
          onChangeAttributes={onAttributesChange}
          onClearAttributes={onClearAttributes}
        />
      </div>
      <div className="file-viewer__main-content">
        <div className="file-viewer__video_and_attributes">
          <div className="file-viewer__video">
            <div className="file-viewer__video_filename">
              {!file.fileName || file.apiStatus === 'loading' ? (
                <Skeleton
                  className="file-viewer__video_filename skeleton"
                  variant="rectangular"
                />
              ) : (
                <div className="file-viewer__video_filename-edit">
                  {isEditing ? (
                    <>
                      <TextField
                        hiddenLabel
                        variant="outlined"
                        value={editableFileName}
                        onChange={(e) => {
                          setEditableFileName(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            if (fileId) {
                              dispatch(
                                updateFileById({
                                  fileId: fileId,
                                  fileName: editableFileName,
                                })
                              );
                            }
                            setEditableFileName('');
                            setIsEditing(false);
                          }
                        }}
                        slotProps={{
                          htmlInput: {
                            'data-testid': 'file-change-name',
                          },
                        }}
                        sx={inputStyle}
                      />
                      <Tooltip title="Cancel" arrow key="cancel">
                        <CancelOutlinedIcon
                          className="file-icon"
                          data-testid="cancel-file-name-button"
                          onClick={() => {
                            setEditableFileName('');
                            setIsEditing(false);
                          }}
                        />
                      </Tooltip>
                      <Tooltip title="Save" arrow key="save">
                        <SaveOutlinedIcon
                          className="file-icon"
                          data-testid="save-file-name-button"
                          onClick={() => {
                            if (fileId) {
                              dispatch(
                                updateFileById({
                                  fileId: fileId,
                                  fileName: editableFileName.trim(),
                                })
                              );
                            }
                            setEditableFileName('');
                            setIsEditing(false);
                          }}
                        />
                      </Tooltip>
                    </>
                  ) : (
                    <>
                      <div data-testid="file-heading-name">{file.fileName}</div>
                      <Tooltip title="Edit File name" arrow key="edit">
                        <EditNoteOutlinedIcon
                          className="file-icon"
                          data-testid="edit-file-name-button"
                          onClick={() => {
                            setEditableFileName(file.fileName);
                            setIsEditing(true);
                          }}
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              )}
            </div>
            <div className="file-viewer__video_player">
              {streams ? (
                <MediaPlayer
                  uri=""
                  ref={playerRef}
                  streams={streams}
                  frameRate={frameRate}
                  boundingBoxes={boundingBoxes}
                  maxHeight={500}
                  thumbnailAssets={thumbnailAssets}
                  readOnly={!enableTrackletsIntersection}
                  initialBoundingBoxPositions={initialBoundingBoxPositions}
                  onAddBoundingBox={({
                    id,
                    boundingPoly,
                  }: {
                    id: string;
                    boundingPoly: BoundingPoly;
                  }) => {
                    if (fileId) {
                      const { pageSize } =
                        trackletType === 'person'
                          ? fileResults.person
                          : fileResults.vehicle;

                      dispatchGetFileResults(
                        getFileResultsByRegion({
                          fileId: fileId,
                          eventId: eventId || '',
                          trackletType,
                          sourceBoundingBox: boundingPoly,
                          page: 1,
                          limit: pageSize,
                        })
                      );
                      setBoundingBoxLocalStorage(fileId, id, boundingPoly);
                      dispatch(
                        updateNumberOfSourceBoundingBoxes(
                          numberOfSourceBoundingBoxes + 1
                        )
                      );
                    }
                    dispatch(
                      setSelectedAttributes({
                        person: {},
                        vehicle: {},
                      })
                    );
                  }}
                  onChangeBoundingBox={({
                    id,
                    boundingPoly,
                  }: {
                    id: string;
                    boundingPoly: BoundingPoly;
                  }) => {
                    if (fileId) {
                      const { pageSize } =
                        trackletType === 'person'
                          ? fileResults.person
                          : fileResults.vehicle;

                      dispatchGetFileResults(
                        getFileResultsByRegion({
                          fileId: fileId,
                          eventId: eventId || '',
                          trackletType,
                          sourceBoundingBox: boundingPoly,
                          page: 1,
                          limit: pageSize,
                        })
                      );
                      setBoundingBoxLocalStorage(fileId, id, boundingPoly);
                    }
                  }}
                  onDeleteBoundingBox={(id: string) => {
                    const { pageSize } =
                      trackletType === 'person'
                        ? fileResults.person
                        : fileResults.vehicle;

                    deleteBoundingBoxLocalStorage(id);
                    dispatchGetFileResults(
                      getFileResultsById({
                        fileId: fileId || '',
                        eventId: eventId || '',
                        trackletType,
                        page: 1,
                        limit: pageSize,
                      })
                    );
                    dispatch(
                      updateNumberOfSourceBoundingBoxes(
                        numberOfSourceBoundingBoxes - 1
                      )
                    );
                  }}
                  actionMenuItems={actionMenuItems}
                  resetOverlayTrigger={resetOverlayTrigger}
                  maxBoundingBoxes={1}
                />
              ) : (
                <Skeleton
                  className="file-viewer__video_player skeleton"
                  variant="rectangular"
                  height={300}
                />
              )}
            </div>
          </div>
          <div className="file-viewer__tracklet-detail">
            {noFile && (
              <div
                className="file-viewer__tracklet-detail-no-file"
                data-testid="file-viewer-tracklet-detail-no-file"
              >
                Select a&nbsp;<b>Detection</b>&nbsp;to View Details.
              </div>
            )}
            {fileLoading ? (
              <>
                <Skeleton
                  className="file-viewer__tracklet-detail-loading"
                  variant="rectangular"
                  height={48}
                />
                <Skeleton
                  className="file-viewer__tracklet-detail-loading file-viewer__accordion-ai-engines"
                  variant="rectangular"
                  height={48}
                />
                <Skeleton
                  className="file-viewer__tracklet-detail-loading"
                  variant="rectangular"
                  height={48}
                />
              </>
            ) : (
              <>
                {hasFile && (
                  <>
                    <Accordion
                      expanded={expanded === 'attributes'}
                      onChange={handleDetailAccordionChange('attributes')}
                    >
                      <AccordionSummary>Detected Attributes</AccordionSummary>
                      <AccordionDetails>
                        <Box
                          className="detected-attributes__box"
                          display="flex"
                          gap={1}
                          flexWrap="wrap"
                        >
                          <DetectedAttributes
                            attributes={
                              currentTab === 'people'
                                ? selectedTracklet.person?.attributes
                                : selectedTracklet.vehicle?.attributes
                            }
                          />
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                    <Accordion
                      className="file-viewer__accordion-ai-engines"
                      expanded={expanded === 'ai-engines'}
                      onChange={handleDetailAccordionChange('ai-engines')}
                    >
                      <AccordionSummary>AI Engines</AccordionSummary>
                      <AccordionDetails>
                        <Box
                          className="detected-attributes__box"
                          display="flex"
                          gap={1}
                          flexWrap="wrap"
                        >
                          Vehicle and Person Detection
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                    <Accordion
                      expanded={expanded === 'file-meta'}
                      onChange={handleDetailAccordionChange('file-meta')}
                    >
                      <AccordionSummary>File Metadata</AccordionSummary>
                      <AccordionDetails>
                        <FileMetadata {...file} />
                      </AccordionDetails>
                    </Accordion>
                  </>
                )}
              </>
            )}
          </div>
        </div>
        <div className="file-viewer__detail">
          <div
            className="file-viewer__tabbed-detections-main-container"
            data-testid="tabbed-detections"
          >
            <div className="file-viewer__tabbed-detections-wrapper">
              <div className="file-viewer__tabbed-detections-title">
                {I18nTranslate.TranslateMessage('detections')}
              </div>
              {numberOfSourceBoundingBoxes > 0 && (
                <Button
                  className="file-viewer__tabbed-detections-clearall-button"
                  variant="text"
                  onClick={() => {
                    setResetOverlayTrigger(!resetOverlayTrigger);
                    dispatch(updateNumberOfSourceBoundingBoxes(0));
                    onClearAttributes();
                  }}
                >
                  {I18nTranslate.TranslateMessage('resetSelection')}
                </Button>
              )}
            </div>
            {/* Tabs */}
            <div className="file-viewer__tabbed-detections-tabs-container">
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                aria-label="disabled tabs example"
              >
                <Tab
                  value="people"
                  label={intl.formatMessage({ id: 'people', defaultMessage: 'people' })}
                  data-testid="people-tab"
                />
                <Tab
                  value="vehicles"
                  label={intl.formatMessage({ id: 'vehicles', defaultMessage: 'Vehicles' })}
                  data-testid="vehicles-tab"
                />
              </Tabs>
            </div>
            <Divider />
            {currentTab === 'people' && (
              <>
                <div className="file-viewer__tabbed-detections-tab-panels main__scrollbar">
                  <div
                    className="main__tracklet_thumbnails-container"
                    data-testid="person-matched-detection-tab"
                  >
                    <div className="main__tracklet_thumbnails-tracklets">
                      {fileResultsLoading ? (
                        range(25).map((i) => (
                          <Skeleton
                            className="main__tracklet_thumbnails-tracklet skeleton"
                            data-testid={`main__tracklet_thumbnails-tracklet-person-skeleton-${i}`}
                            key={`Person-matched-detection-tab-tracklet-${i}`}
                            variant="rectangular"
                            style={{
                              width: `${(113 * thumbnailScale) / 100}px`,
                              height: `${(113 * thumbnailScale) / 100}px`,
                            }}
                          />
                        ))
                      ) : fileResults.person &&
                        fileResults.person.results.length > 0 ? (
                        fileResults.person.results?.map((tracklet, index) => {
                          const selected =
                            selectedTracklet?.person?.trackletId ===
                            tracklet?.trackletId;
                          const thumbnailUrl =
                            thumbnails?.[tracklet?.trackletId]?.thumbnailUrls
                              .best;
                          const thumbnailIsExpired =
                            new Date(
                              thumbnails?.[
                                tracklet?.trackletId
                              ]?.expiresDateTime
                            ) <= new Date();

                          return thumbnailUrl && !thumbnailIsExpired ? (
                            <TrackletComp
                              onTrackletStartTimeClick={(time) => {
                                const seekTime =
                                  Math.ceil((time / 1000) * frameRate) /
                                  frameRate;
                                playerRef?.current?.seek(seekTime);
                              }}
                              onTrackletStopTimeClick={(time) => {
                                const seekTime =
                                  Math.ceil((time / 1000) * frameRate) /
                                  frameRate;
                                playerRef?.current?.seek(seekTime);
                              }}
                              thumbnailUrl={thumbnailUrl}
                              thumbnailScale={thumbnailScale}
                              tracklet={tracklet}
                              selected={selected}
                              handleTrackletClick={handleTrackletClick}
                              index={index}
                              checked={selected}
                              playing={isPlaying}
                            />
                          ) : (
                            <TrackletLoading
                              thumbnailScale={thumbnailScale}
                              index={index}
                            />
                          );
                        })
                      ) : (
                        <div
                          className="file-viewer__no-tracklets-found"
                          data-testid="file-viewer-no-person-tracklets-found"
                        >
                          {intl.formatMessage({ id: 'noTrackletsDetected', defaultMessage: 'No detections Detected' })}.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <Divider />
                <div className="file-viewer__tabbed-detections-footer">
                  <Pagination
                    prevPage={changePage('person', -1)}
                    nextPage={changePage('person', 1)}
                    setPageSize={setPageSize('person')}
                    totalCount={fileResults.person.totalCount}
                    currentPage={fileResults.person.currentPage}
                    totalPages={fileResults.person.totalPages}
                    pageSize={fileResults.person.pageSize}
                    loading={fileResults.person.apiStatus === 'loading'}
                    setPage={(page) => changePage('person', undefined, page)()}
                  />
                  <ThumbnailScaler
                    scale={thumbnailScale}
                    setScale={setThumbnailScale}
                    loading={fileResults.person.apiStatus === 'loading'}
                  />
                </div>
              </>
            )}
            {currentTab === 'vehicles' && (
              <>
                <div className="file-viewer__tabbed-detections-tab-panels main__scrollbar">
                  <div
                    className="file-viewer__person-matched-detection-tab__container"
                    data-testid="vehicle-matched-detection-tab"
                  >
                    <div className="main__tracklet_thumbnails-tracklets">
                      {fileResultsLoading ? (
                        range(25).map((i) => (
                          <Skeleton
                            className="main__tracklet_thumbnails-tracklet skeleton"
                            data-testid={`main__tracklet_thumbnails-tracklet-vehicle-skeleton-${i}`}
                            key={`Vehicle-matched-detection-tab-tracklet-${i}`}
                            variant="rectangular"
                            style={{
                              width: `${(113 * thumbnailScale) / 100}px`,
                              height: `${(113 * thumbnailScale) / 100}px`,
                            }}
                          />
                        ))
                      ) : fileResults.vehicle &&
                        fileResults.vehicle.results.length > 0 ? (
                        fileResults.vehicle.results?.map((tracklet, index) => {
                          const selected =
                            selectedTracklet?.vehicle?.trackletId ===
                            tracklet?.trackletId;
                          const thumbnailUrl =
                            thumbnails?.[tracklet?.trackletId]?.thumbnailUrls
                              .best;
                          const thumbnailIsExpired =
                            new Date(
                              thumbnails?.[
                                tracklet?.trackletId
                              ]?.expiresDateTime
                            ) <= new Date();
                          return thumbnailUrl && !thumbnailIsExpired ? (
                            <TrackletComp
                              selected={selected}
                              onTrackletStartTimeClick={(time) => {
                                const seekTime =
                                  Math.ceil((time / 1000) * frameRate) /
                                  frameRate;
                                playerRef?.current?.seek(seekTime);
                              }}
                              onTrackletStopTimeClick={(time) => {
                                const seekTime =
                                  Math.ceil((time / 1000) * frameRate) /
                                  frameRate;
                                playerRef?.current?.seek(seekTime);
                              }}
                              handleTrackletClick={handleTrackletClick}
                              thumbnailUrl={thumbnailUrl}
                              thumbnailScale={thumbnailScale}
                              tracklet={tracklet}
                              index={index}
                              checked={selected}
                              playing={isPlaying}
                            />
                          ) : (
                            <TrackletLoading
                              thumbnailScale={thumbnailScale}
                              index={index}
                            />
                          );
                        })
                      ) : (
                        <div
                          className="file-viewer__no-tracklets-found"
                          data-testid="file-viewer-no-vehicle-tracklets-found"
                        >
                          {I18nTranslate.TranslateMessage(
                            'noTrackletsDetected'
                          )}
                          .
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <Divider />
                <div className="file-viewer__tabbed-detections-footer">
                  <Pagination
                    prevPage={changePage('vehicle', -1)}
                    nextPage={changePage('vehicle', 1)}
                    setPageSize={setPageSize('vehicle')}
                    totalCount={fileResults.vehicle.totalCount}
                    currentPage={fileResults.vehicle.currentPage}
                    totalPages={fileResults.vehicle.totalPages}
                    pageSize={fileResults.vehicle.pageSize}
                    loading={fileResults.vehicle.apiStatus === 'loading'}
                    setPage={(page) => changePage('vehicle', undefined, page)()}
                  />
                  <ThumbnailScaler
                    scale={thumbnailScale}
                    setScale={setThumbnailScale}
                    loading={fileResults.vehicle.apiStatus === 'loading'}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileViewer;

interface ActionMenuItem {
  label: string;
  onClick: ({
    id,
    boundingPoly,
  }: {
    id: string;
    boundingPoly: BoundingPoly;
  }) => void;
  isDelete?: boolean;
}
