import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, ActionValidationError } from '@common/errors';
import * as ResTypes from '../../../../../../types/responses';
import env from '../../../../env';
import EventAlreadyCreatedForFolder from '@common/errors/EventAlreadyCreatedForFolder';
import { TRACK_FILE_TAG } from '../createFileTemporalData';

const importFolder = async <ReqPayload, Data>(
  context: Context<
    ReqPayload,
    Data &
      responses.getParentFolderId & {
        eventId?: string;
        eventSchemaIds?: string[];
      } & responses.getMe &
      Partial<responses.getFolder>
  >
): Promise<
  | Context<
      ReqPayload,
      Data & Partial<ResTypes.CreateEventPayloadResponse & responses.getMe>
    >
  | undefined
> => {
  const { cache, data, req } = context;

  const headers = { Authorization: req.headers.authorization };
  const { folderId, eventStartDate, eventEndDate, description, name, tdoId } =
    req.body;

  if (!folderId) {
    throw new ActionValidationError('Folder ID is missing');
  }

  if (!data.eventSchemaIds || data.eventSchemaIds.length === 0) {
    throw new ActionValidationError('Event Schema IDs are missing');
  }

  const { temporalDataObject } = await callGQL<
    responses.getFileTemporalData,
    ReqPayload,
    Data
  >(context, headers, queries.getFileTemporalData, {
    tdoId,
  });

  if (
    !temporalDataObject?.details?.tags?.find((t) => t.value === TRACK_FILE_TAG)
  ) {
    await callGQL<unknown, ReqPayload, Data>(
      context,
      headers,
      queries.updateTDOwithTrackTag,
      {
        tdoId,
        details: {
          ...(temporalDataObject?.details ?? {}),
          tags: [
            ...(temporalDataObject?.details?.tags ?? []),
            { value: TRACK_FILE_TAG },
          ],
        },
      }
    );
  }

  const { folder } = await callGQL<responses.getFolder, ReqPayload, Data>(
    context,
    headers,
    queries.getFolder,
    { folderId }
  );

  const event = folder?.contentTemplates.find((template) =>
    data.eventSchemaIds?.includes(template.sdo.schemaId)
  );

  if (event) {
    throw new EventAlreadyCreatedForFolder(
      'Track event already created for folder'
    );
  }

  const schemaId = cache.get('eventsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }
  const currentTime = new Date().toISOString();

  const { userId, firstName, lastName, parentFolderId } = data;

  if (!userId || !parentFolderId) {
    throw new ActionError(
      `Missing required data: ${[!firstName ? 'firstName' : '', !userId ? 'userId' : '', !parentFolderId ? 'parentFolderId' : ''].filter(Boolean).join(', ')}`
    );
  }

  const { createStructuredData } = await callGQL<
    responses.createStructuredData,
    ReqPayload,
    Data
  >(context, headers, queries.createStructuredData, {
    id: '',
    schemaId,
    data: {
      id: folderId,
      name: name ?? 'Imported',
      description: description ?? '',
      createdBy: userId,
      createdByName: `${firstName ?? ''} ${lastName ?? ''}`,
      eventStartDate,
      eventEndDate,
      tags: [],
      trackerEngineId: env.trackerEngineId,
      createdDateTime: currentTime,
      modifiedDateTime: currentTime,
    },
  });

  if (createStructuredData) {
    const new_data = Object.assign({}, data, {
      createStructuredData,
      createFolder: folder,
    });
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  }
};

export default importFolder;
