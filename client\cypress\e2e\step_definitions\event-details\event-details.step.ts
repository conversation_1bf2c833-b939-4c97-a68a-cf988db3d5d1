import { When, Then, Before, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { eventDetailsPage } from '../../../pages/eventDetailsPage';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';

Before({ tags: '@event-details' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/events\/\?sortBy/).as('fetchEvent');
});

Then('The user should see {string} in the event detail panel', (eventName: string) => {
  eventDetailsPage.verifyEventDetailPanel(eventName);
});

Then('The user should see the header with application name {string}', (appName: string) => {
  eventDetailsPage.verifyHeaderAppName(appName);
});

Then('The user should see the breadcrumb containing the event name {string}', (eventName: string) => {
  eventDetailsPage.verifyBreadcrumb(eventName);
});

Then('The user should see the event title {string}', (eventTitle: string) => {
  eventDetailsPage.verifyEventTitle(eventTitle);
});

Then('The user should see the {string} and {string} buttons', (uploadButton: string, searchButton: string) => {
  eventDetailsPage.verifyButtons(uploadButton, searchButton);
});

Then('The user should see the tabs {string}, {string}, and {string}', (filesTab: string, matchTab: string, timelineTab: string) => {
  eventDetailsPage.verifyTabs(filesTab, matchTab, timelineTab);
});

Then('The user should not see the table loading skeleton', () => {
  eventDetailsPage.verifyTableLoadingSkeletonNotVisible();
});

When('The user clicks {string} breadcrumb in Event Screen details', (breadcrumbText: string) => {
  eventDetailsPage.clickBreadcrumb(breadcrumbText);
});

Then('The user should be navigated back to the main Event Screen', () => {
  eventDetailsPage.verifyNavigationToMainScreen();
});

When('The user clicks the edit event button', () => {
  eventDetailsPage.clickEditEvent();
});

When('The user enters {string} into the event name field', (newEventName: string) => {
  eventDetailsPage.enterEventName(newEventName);
});

When('The user enters {string} into the description field', (description: string) => {
  eventDetailsPage.enterDescription(description);
});

When('The user adds the tag {string}', (tagName: string) => {
  eventDetailsPage.addTag(tagName);
});

When('The user sets the start date to {string} of {string}', (day: string, month: string) => {
  eventDetailsPage.setStartDate(day, month);
});

When('The user clicks the save event button', () => {
  eventDetailsPage.clickSaveEvent();
});

Then('The event title should be updated to {string}', (updatedName: string) => {
  eventDetailsPage.verifyEventTitleUpdated(updatedName);
});

Then('The event description should be updated to {string}', (updatedDescription: string) => {
  eventDetailsPage.verifyEventDescriptionUpdated(updatedDescription);
});

Then('The event tags should include {string}', (tagName: string) => {
  eventDetailsPage.verifyEventTagsUpdated(tagName);
});

Then('The event date range should start with {string}', (expectedDateString: string) => {
  eventDetailsPage.verifyEventDateUpdated(expectedDateString);
});

When('The user enters the long description {string}', (longDescription: string) => {
  eventDetailsPage.enterLongDescription(longDescription);
});

Then('The long description is displayed correctly and wraps within its container', () => {
  eventDetailsPage.verifyLongDescription();
});

Then('The user changes and verifies the results per page for the following values:', (dataTable: DataTable) => {
  eventDetailsPage.changeResultsPerPage(dataTable);
});

When('The user clicks the {string} page button', (direction: 'next' | 'previous') => {
  eventDetailsPage.clickPageButton(direction);
});

Then('The {string} page button should be {string}', (direction: 'next' | 'previous', state: 'enabled' | 'disabled') => {
  eventDetailsPage.verifyPageButtonState(direction, state);
});

When('The user clicks the {string} button', (buttonText: string) => {
  eventDetailsPage.clickButton(buttonText);
});

Then('The Search All Files dialog should open and display a list of attributes', () => {
  eventDetailsPage.verifySearchAllFilesDialog();
});

Then('The dialog should display the following elements:', (dataTable: DataTable) => {
  eventDetailsPage.verifyDialogElements(dataTable);
});

When('The user selects the {string} option in the search dialog', (option: string) => {
  eventDetailsPage.selectSearchDialogOption(option);
});

Then('The dialog should display the following attributes for {string}:', (type: string, dataTable: DataTable) => {
  eventDetailsPage.verifyDialogAttributes(type, dataTable);
});

Then('The attribute selected count should show {int}', (count: number) => {
  eventDetailsPage.verifyAttributeSelectedCount(count);
});

When('The user selects the attribute category {string}', (category: string) => {
  eventDetailsPage.selectAttributeCategory(category);
});

When('The user {string} the box for the attribute {string}', (action: 'checks' | 'un-checks', attribute: string) => {
  eventDetailsPage.toggleAttributeCheckbox(action, attribute);
});

Then('The user should see the notification message {string}', (message: string) => {
  eventDetailsPage.verifyNotificationMessage(message);
});

When('The user clicks the {string} button in the search dialog', (buttonText: string) => {
  eventDetailsPage.clickButtonInSearchDialog(buttonText);
});

When('The user "checks" the boxes for the following attributes:', (dataTable: DataTable) => {
  eventDetailsPage.checkAttributes(dataTable);
});

When('The user creates a new match group named {string}', (matchGroupName: string) => {
  eventDetailsPage.createNewMatchGroup(matchGroupName);
});

When('The user clicks the {string} tab', (tabName: string) => {
  eventDetailsPage.clickTab(tabName);
});

Then('The match group {string} should be listed with {string} searches', (matchGroupName: string, searchCount: string) => {
  eventDetailsPage.verifyMatchGroupInList(matchGroupName, searchCount);
});

When('The user deletes the match group {string}', (matchGroupName: string) => {
  eventDetailsPage.deleteMatchGroup(matchGroupName);
});

When('The user clicks the {string} button in the confirmation dialog', (buttonText: string) => {
  eventDetailsPage.clickButtonInConfirmationDialog(buttonText);
});

Then('The match group {string} should no longer be listed', (matchGroupName: string) => {
  eventDetailsPage.verifyMatchGroupNotInList(matchGroupName);
});

Then('The search dialog should be closed', () => {
  eventDetailsPage.verifySearchDialogClosed();
});
