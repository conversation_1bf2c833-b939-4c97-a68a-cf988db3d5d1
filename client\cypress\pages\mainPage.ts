import { DataTable } from '@badeball/cypress-cucumber-preprocessor';
import {
  ButtonTestIds,
  LandingPageTestIds,
  SearchTestIds,
  SnackbarTestIds,
  buttonSelectors,
  dialogSelectors,
  eventDetailSelectors,
  fileDetailSelectors,
} from '../support/helperFunction/activeTabHelper';
export const mainPage = {
  visit: (): void => {
    cy.visit('/');
    cy.waitMainPageIsLoaded();
    cy.url().should('include', 'activeTab=events');
  },

  verifyColumnHeaders: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.COLUMN_0 }).as('column0');
    cy.get('@column0').should('be.visible');
    cy.get('@column0').should('contain.text', 'Event Name');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.COLUMN_1 }).as('column1');
    cy.get('@column1').should('be.visible');
    cy.get('@column1').should('contain.text', 'Event Time');
  },

  verifyAppTitle: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.APPBAR_TITLE }).as('appTitle');
    cy.get('@appTitle').should('be.visible');
    cy.get('@appTitle').should('have.text', 'Track');
  },

  clickTab: (tabName: string) => cy.get(`[data-testid="home-${tabName.toLowerCase()}-tab"]`).click(),

  verifyTabIsActive: (activeTabName: string) => {
    const activeTab = activeTabName.toLowerCase();
    const inactiveTab = activeTab === 'events' ? 'files' : 'events';

    cy.get(`[data-testid="home-${activeTab}-tab"]`).should('have.attr', 'aria-selected', 'true');
    cy.get(`[data-testid="home-${inactiveTab}-tab"]`).should('have.attr', 'aria-selected', 'false');
  },

  clickBreadcrumb: (breadcrumbText: string) => {
    cy.url().as('initialUrl');
    cy.contains('nav[aria-label="breadcrumb"] a', breadcrumbText).click({ force: true });
  },

  verifyPageNotNavigated: () => {
    cy.get('@initialUrl').then((initialUrl) => {
      cy.url().should('eq', initialUrl);
    });
    cy.contains('nav[aria-label="breadcrumb"] a', 'All Events').should('be.visible');
  },

  selectItem: (name: string, type: 'event' | 'file') => {
    const selectionStrategies = {
      event: () => {
        cy.get('[data-testid="pending-event-row-name"]').should('not.exist');
        return cy.contains('[data-testid="event-row-name"]', name);
      },
      file: () => cy.contains(name).closest('[data-testid^="file-row-"]'),
    };
    selectionStrategies[type]().should('be.visible').click();
  },

  verifyDetails: (rows: { Field: string; 'Expected Value': string }[], type: 'event' | 'file') => {
    cy.log(`Starting ${type} details verification...`);
    const selectorMap = type === 'event' ? eventDetailSelectors : fileDetailSelectors;

    rows.forEach((row) => {
      const field = row.Field;
      // const expectedValue = row['Expected Value'];
      const detailConfig = selectorMap[field];

      if (!detailConfig) {
        throw new Error(`The field "${field}" is not a valid option to check in ${type} details.`);
      }
    });

    cy.log(`All ${type} details verified successfully.`);
  },

  verifyTimeFormat: (format: string, type: 'event' | 'file') => {
    let selector;
    if (type === 'event') {
      selector = '[data-testid^="event-row-cell-"][data-testid$="-1"]';
    } else if (type === 'file') {
      selector = '[data-testid^="file-row-cell-"][data-testid$="-2"]';
    } else {
      throw new Error(`Unsupported type: ${type}`);
    }

    cy.get(selector)
      .first()
      .invoke('text')
      .then((timeText) => {
        cy.log(`Verifying time format for: "${timeText}"`);
        const timeFormatRegex = /^\d{1,2}\/\d{1,2}\/\d{4}, \d{1,2}:\d{2} (AM|PM)$/;
        expect(timeText.trim()).to.match(timeFormatRegex, `${type} time "${timeText}" should match format ${format}`);
      });
  },

  clickDetailNameEdit: (type: 'event' | 'file') => {
    const selectorMap = {
      event: ButtonTestIds.EVENT_NAME_EDIT,
      file: ButtonTestIds.FILE_NAME_EDIT,
    };

    const selector = selectorMap[type];
    cy.log(`Clicking edit for ${type} using selector: ${selector}`);
    cy.get(`[data-testid="${selector}"]`).should('be.visible').click();
  },

  changeDetailName: (newName: string, type: 'event' | 'file') => {
    const selectorMap = {
      event: LandingPageTestIds.HOME_DETAIL_EVENT_NAME_TEXTFIELD,
      file: LandingPageTestIds.HOME_DETAIL_FILE_NAME_TEXTFIELD,
    };

    const selector = selectorMap[type];
    cy.get(`[data-testid="${selector}"]`).as('nameInput');
    cy.get('@nameInput').should('be.visible');
    cy.get('@nameInput').clear();
    cy.get('@nameInput').type(`${newName}{enter}`);
    cy.wrap(newName).as('expectedName');
  },

  verifyDetailNameChanged: (expectedName: string, type: 'event' | 'file') => {
    cy.log(`Verifying ${type} name is displayed as "${expectedName}"`);
    const verificationStrategies = {
      event: () => cy.get(`[data-testid="${LandingPageTestIds.HOME_DETAIL_NAME}"]`),
      file: () => cy.contains(expectedName),
    };
    const nameDisplayElement = verificationStrategies[type]();
    nameDisplayElement.should('be.visible');
    nameDisplayElement.should('contain.text', expectedName);
  },

  clickViewEventButton: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.VIEW_EVENT].selector }).as('viewButton');
    cy.get('@viewButton').should('be.visible');
    cy.get('@viewButton').click();
  },

  verifyNavigationToEventDetails: () => {
    cy.url().should('include', '/event/');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.EVENT_CONTAINER }).should('be.visible');
  },

  clickDeleteEventButton: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.DELETE_EVENT].selector }).as('deleteButton');
    cy.get('@deleteButton').should('be.visible');
    cy.get('@deleteButton').click();
  },

  clickDeleteFileButton: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.DELETE_FILE].selector }).as('deleteFileButton');
    cy.get('@deleteFileButton').should('be.visible');
    cy.get('@deleteFileButton').click();
  },

  enterEventNameToSearchInput: (name: string) => {
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).as('dialogInput');
    cy.get('@dialogInput').should('be.visible');
    cy.get('@dialogInput').clear();
    cy.get('@dialogInput').type(name);
  },

  verifyDeleteButtonDisabledAndTextboxError: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('be.disabled');
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).should('have.attr', 'aria-invalid', 'true');
  },

  clickColumnHeader: (columnName: string, type: 'event' | 'file' = 'event') => {
    const eventColumnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
    };

    const fileColumnMap: { [key: string]: string } = {
      'File Name': LandingPageTestIds.COLUMN_0,
      'Upload Date': LandingPageTestIds.COLUMN_2,
    };

    const columnMap = type === 'file' ? fileColumnMap : eventColumnMap;
    cy.getDataIdCy({ idAlias: columnMap[columnName] }).click();

    if (type === 'file') {
      cy.waitFilesTabIsLoaded();
    } else {
      cy.waitMainPageIsLoaded();
    }
  },

  clickColumnHeaderUntilSorted: (columnName: string, sortedBy: string, type: 'event' | 'file' = 'event') => {
    const eventColumnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
    };

    const fileColumnMap: { [key: string]: string } = {
      'File Name': LandingPageTestIds.COLUMN_0,
      'Upload Date': LandingPageTestIds.COLUMN_2,
    };

    const columnMap = type === 'file' ? fileColumnMap : eventColumnMap;
    const order = sortedBy === 'a-z' ? 'ascending' : 'descending';

    for (let i = 0; i < 3; i++) {
      cy.getDataIdCy({ idAlias: columnMap[columnName] }).then(($el) => {
        const currentSort = $el.attr('aria-sort');
        if (currentSort !== order) {
          cy.getDataIdCy({ idAlias: columnMap[columnName] }).click();
          if (type === 'file') {
            cy.waitFilesTabIsLoaded();
          } else {
            cy.waitMainPageIsLoaded();
          }
        }
      });
    }
  },

  verifyColumnSortState: (columnName: string, sortedBy: string, type: 'event' | 'file' = 'event') => {
    const eventColumnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
    };

    const fileColumnMap: { [key: string]: string } = {
      'File Name': LandingPageTestIds.COLUMN_0,
      'Upload Date': LandingPageTestIds.COLUMN_2,
    };

    const columnMap = type === 'file' ? fileColumnMap : eventColumnMap;
    const order = sortedBy === 'a-z' ? 'ascending' : 'descending';

    cy.getDataIdCy({ idAlias: columnMap[columnName] }).should('have.attr', 'aria-sort', order);
    cy.assertTableColumnSorted(columnName, sortedBy);
  },

  enterSearchKeyword: (keyword: string) => {
    cy.getDataIdCy({ idAlias: SearchTestIds.SEARCH_INPUT }).as('searchInput');
    cy.get('@searchInput').should('be.visible');
    cy.get('@searchInput').clear();
    cy.get('@searchInput').type(keyword);
  },

  enterSearchEventPopup: (keyword: string) => {
    cy.getDataIdCy({ idAlias: SearchTestIds.SEARCH_POPUP }).clear();
    cy.getDataIdCy({ idAlias: SearchTestIds.SEARCH_POPUP }).type(keyword);
    cy.get('[role="listbox"]').find('[role="option"]').contains(keyword).click();
  },

  verifySearchResults: (keyword: string, searchType: 'events' | 'files') => {
    let nameSelector: string;
    if (searchType === 'files') {
      cy.waitFilesTabIsLoaded();
      nameSelector = '[data-testid^="file-row-cell-"][data-testid$="-0"]';
    } else {
      cy.waitMainPageIsLoaded();
      nameSelector = `[data-testid="${LandingPageTestIds.EVENT_ROW_NAME}"]`;
    }
    cy.get(nameSelector).should('have.length.greaterThan', 0);
    cy.get(nameSelector).each(($el) => {
      cy.wrap($el)
        .invoke('text')
        .then((text) => {
          expect(text.toLowerCase()).to.contain(keyword.toLowerCase());
        });
    });
  },

  verifyResultsPerPageLabel: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).parent().prev().should('contain.text', 'Results Per Page').should('be.visible');
  },

  changeResultsPerPage: (perPage: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).click();
    cy.get('[role="listbox"]').contains(perPage).click();
  },

  verifyResultsPerPageChanged: (perPage: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).should('have.text', perPage);
  },

  verifyPaginationInitialState: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').as('initialPaginationText');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('not.have.class', 'enabled');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_NEXT_BUTTON }).should('have.class', 'enabled');
  },

  clickNextPage: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_NEXT_BUTTON }).click();
  },

  verifyNavigatedToNextPage: () => {
    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').should('not.eq', initialText);
    });
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('have.class', 'enabled');
  },

  clickPreviousPage: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).click();
  },

  verifyNavigatedToPreviousPage: () => {
    cy.get('[data-testid="home-files-tab"]').then(($filesTab) => {
      const isFilesTabActive = $filesTab.attr('aria-selected') === 'true';
      if (isFilesTabActive) {
        cy.waitFilesTabIsLoaded();
      } else {
        cy.waitMainPageIsLoaded();
      }
    });
    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').should('eq', initialText);
    });
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('not.have.class', 'enabled');
  },

  createTestEvent: (eventName: string) => {
    const currentTime = new Date().toISOString();

    cy.request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/api/v1/event`,
      headers: {
        Authorization: `Bearer ${Cypress.env('token')}`,
        'Content-Type': 'application/json',
      },
      body: {
        name: eventName,
        description: 'Test event for deletion testing',
        eventStartDate: currentTime,
        eventEndDate: currentTime,
      },
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body.event).to.have.property('id');
      cy.wrap(response.body.event.id).as('testEventId');
      cy.wrap(eventName).as('testEventName');
      cy.log(`Created test event with ID: ${response.body.event.id}`);
    });
  },

  verifyDeleteConfirmationDialog: (eventName: string) => {
    cy.getDataIdCy({ idAlias: 'confirm-dialog' }).should('be.visible');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-title' }).should('be.visible').and('contain.text', 'Delete Event');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-description' })
      .should('be.visible')
      .and('contain.text', `You are about to delete ${eventName}`)
      .and('contain.text', 'All associated files, match groups, searches, and exports will be removed')
      .and('contain.text', 'Are you sure you want to delete it?');
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).should('be.visible');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-cancel-action' }).should('be.visible').and('not.be.disabled');
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('be.visible').and('be.disabled');
  },

  enterEventNameForDeletion: (eventName: string) => {
    cy.log(`--- Entering event name for deletion: ${eventName} ---`);
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).as('deleteInput');
    cy.get('@deleteInput').should('be.visible');
    cy.get('@deleteInput').clear();
    cy.get('@deleteInput').type(eventName);
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled');
  },

  confirmEventDeletion: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled').click();
    cy.getDataIdCy({ idAlias: 'confirm-dialog' }).should('not.exist');
  },

  verifyEventDeleted: (eventName: string) => {
    cy.waitMainPageIsLoaded();
    cy.get('[data-testid^="event-row-"]').should('exist');
    cy.get('[data-testid^="event-row-"]').each(($row) => {
      cy.wrap($row).should('not.contain.text', eventName);
    });
  },

  clickUploadFilesButton: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILE_BUTTON }).click();
  },

  clickNewEvent: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_BUTTON }).click();
  },

  enterEventName: (eventName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_INPUT }).as('eventNameInput');
    cy.get('@eventNameInput').should('be.visible');
    cy.get('@eventNameInput').clear();
    cy.get('@eventNameInput').type(eventName);
  },

  clickCreateEvent: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_CONFIRM_BUTTON }).click();
  },

  verifySuccessSnackbar: (message: string) => {
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX }).should('be.visible');
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX_SUCCESS }).should('be.visible');
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX }).find('div').last().should('contain.text', message);
  },

  clickCancelUpload: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_BUTTON }).prev().click();
  },

  verifyEventInTable: (eventName: string) => {
    cy.waitMainPageIsLoaded();
    cy.getDataIdCy({ idAlias: LandingPageTestIds.EVENT_ROW_NAME }).should('contain.text', eventName);
  },

  verifyFileInTable: (fileName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PENDING_FILE_ROW }).should('not.exist');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.FILE_ROW_NAME }).should('contain.text', fileName);
  },

  verifyFileNotInTable: (fileName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.FILE_ROW_NAME }).should('not.contain.text', fileName);
  },

  verifyDeleteButtonEnabledAndClick: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled').click();
  },

  clickUploadButton: () => {
    // TODO: Replace with proper wait condition instead of arbitrary wait
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(3000);
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_BUTTON }).click();
  },

  verifyFileUploadedSuccessfully: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILE_INPUT }).should('exist');
  },

  verifyFileAppearsInUploadArea: (fileName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILES_FILE }).should('be.visible').should('contain', fileName);

    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILES_FILE_IDLE }).should('be.visible');
  },

  verifyUploadComplete: (progress: string) => {
    cy.get('.search-and-upload__files-file-progress-text').should('be.visible').and('contain', progress);

    // TODO: Replace with proper wait condition instead of timeout
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILES_FILE_COMPLETE, options: { timeout: 30000 } })
      .should('be.visible')
      .and('contain', 'Complete');
  },

  deleteEventsIfExist: (
    eventName?: string,
    eventNamesDataTable?: DataTable
  ) => {
    const names: string[] = eventName
      ? [eventName]
      : eventNamesDataTable?.hashes().map((r) => r.eventName) || [];

    if (names.length === 0) {
      cy.log('No Event names provided to delete.');
      return;
    }
    cy.request({
      method: 'GET',
      headers: { Authorization: 'Bearer ' + Cypress.env('token') },
      // TO DO: Use dynamic URL
      url: `https://local.veritone.com:3002/api/v1/events?sortBy=eventStartDate&sortDirection=desc` }).then((res) => {
      const records = res.body?.results || [];
      for (const targetEventName of names) {
        const matchingRecords = records.filter((records: { name: string; id: string }) => records.name === targetEventName);
        if (matchingRecords.length === 0) {
          cy.log(`No Event found with name: ${targetEventName}`);
          continue;
        }
        const idsToDelete: string[] = matchingRecords.map((record: { id: string; }) => record.id);
        const deleteRequests = idsToDelete.map(id => {
          const deleteUrl = `https://local.veritone.com:3002/api/v1/event/${id}`;
          return cy.request({
            method: 'DELETE',
            url: deleteUrl,
            headers: {
              'Authorization': `Bearer ${Cypress.env('token')}`
            } }).then((deleteRes) => {
            if (deleteRes.status === 200) {
              cy.log(`Successfully deleted event ID: ${id}`);
            } else {
              cy.log(`Failed to delete event ID: ${id}. Status: ${deleteRes.status}, Body: ${JSON.stringify(deleteRes.body)}`);
            }
          });
        }
        )
        Cypress.Promise.all(deleteRequests).then(() => {
          cy.log(`Finished attempting to delete ${idsToDelete.length} events.`);
        });
      }
    })
  },

  deleteFilesIfExist: (
    fileName?: string,
    fileNamesDataTable?: DataTable
  ) => {
    const names: string[] = fileName
      ? [fileName]
      : fileNamesDataTable?.hashes().map((r) => r.fileName) || [];

    if (names.length === 0) {
      cy.log('No File names provided to delete.');
      return;
    }
    cy.request({
      method: 'GET',
      headers: { Authorization: 'Bearer ' + Cypress.env('token') },
      // TO DO: Use dynamic URL
      url: `https://local.veritone.com:3002/api/v1/files/?pageSize=50&currentPage=1&sortBy=createdTime&sortDirection=desc` }).then((res) => {
      const records = res.body?.results || [];
      for (const targetFileName of names) {
        const matchingRecords = records.filter((records: { name: string; id: string }) => records.name === targetFileName);
        if (matchingRecords.length === 0) {
          cy.log(`No File found with name: ${targetFileName}`);
          continue;
        }
        const idsToDelete: string[] = matchingRecords.map((record: { id: string; }) => record.id);
        const deleteRequests = idsToDelete.map(id => {
          const deleteUrl = `https://local.veritone.com:3002/api/v1/file/${id}`;
          return cy.request({
            method: 'DELETE',
            url: deleteUrl,
            headers: {
              'Authorization': `Bearer ${Cypress.env('token')}`
            } }).then((deleteRes) => {
            if (deleteRes.status === 200) {
              cy.log(`Successfully deleted event ID: ${id}`);
            } else {
              cy.log(`Failed to delete event ID: ${id}. Status: ${deleteRes.status}, Body: ${JSON.stringify(deleteRes.body)}`);
            }
          });
        }
        )
        Cypress.Promise.all(deleteRequests).then(() => {
          cy.log(`Finished attempting to delete ${idsToDelete.length} files.`);
        });
      }
    })
  },

  verifyNoSearchResultsFound: () => {
    cy.contains('div', 'Search Results').siblings().should('contain.text', '0 items');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_EVENTS_TAB }).should('be.visible').and('contain.text', '0');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_FILES_TAB }).should('be.visible').and('contain.text', '0');
  },

  clickViewFileButtonInFileDetail: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_VIEW_FILE_BUTTON }).should('be.visible').click();
  },

  verifyViewFileButtonIsDisabled: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_VIEW_FILE_BUTTON }).should('have.css', 'opacity', '1');
  },
};
