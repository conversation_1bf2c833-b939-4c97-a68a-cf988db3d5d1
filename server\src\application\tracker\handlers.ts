import Redis from 'ioredis';
import { ActionError } from '@common/errors';
import NodeCache from 'node-cache';
import { schemas } from './schemas';
import localActions from './actions';
import commonActions from '../common/actions';
import { NextFunction, Response } from 'express';
import { RequestWithMeta } from '../types';
import * as ResTypes from '../../../../types/responses';
import * as ReqTypes from '../../../../types/requests';
import { responses } from './graphQL';
import {
  CheckCreateRootFolderStatus,
  TrackletBoundingBox,
} from '../../../../types/tracker';
import RedisWrapper from 'src/redisWrapper';
import { SearchTrackletsWithIntersectingBoxData } from './actions/searchTrackletsWithIntersectingBox';

const A = { ...localActions, ...commonActions };

const createHandlers = ({
  log,
  cache,
  redis,
  redisWrapper,
}: {
  log: Logger;
  cache: NodeCache;
  redis?: Redis;
  redisWrapper?: RedisWrapper;
}) => ({
  get: {
    event: async (
      req: RequestWithMeta<ReqTypes.GetEventPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.getMe)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getRedisEvent)
        .then(async (cxt) => {
          const { redisEvent } = cxt.data;
          if (redisEvent) {
            return A.send<ResTypes.GetEventResponse, ReqTypes.GetEventPayload>(
              () => ({
                event: redisEvent,
              })
            )(cxt);
          } else {
            return Promise.resolve(cxt)
              .then(A.getAllEventSchemaIds)
              .then(A.getEvent)
              .then(A.getMatchGroups)
              .then(A.searchFiles)
              .then(A.setRedisEvent)
              .then(
                A.send<
                  ResTypes.GetEventResponse,
                  responses.getEvent &
                    responses.getMatchGroups &
                    responses.searchFiles
                >((cxt) => ({
                  event: {
                    ...cxt.data.event,
                    matchGroupsCount:
                      cxt.data.matchGroups.pagination.totalCount,
                    filesCount: cxt.data.searchFiles.totalCount,
                  },
                }))
              );
          }
        })
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    events: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
        validations: [
          { schema: schemas.get.query.events, validationData: req.query },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.searchEvents)
        .then(
          A.send<ResTypes.SearchEventsResponse, responses.searchEvents>(
            (cxt) => ({
              results: cxt.data.searchEvents.searchResults,
              currentPage: cxt.data.searchEvents.currentPage,
              pageSize: cxt.data.searchEvents.pageSize,
              totalCount: cxt.data.searchEvents.totalCount,
              totalPages: cxt.data.searchEvents.totalPages,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    eventsByIds: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.searchEventsByIds)
        .then(
          A.send<ResTypes.SearchEventsResponse, responses.searchEvents>(
            (cxt) => ({
              results: cxt.data.searchEvents.searchResults,
              currentPage: cxt.data.searchEvents.currentPage,
              pageSize: cxt.data.searchEvents.pageSize,
              totalCount: cxt.data.searchEvents.totalCount,
              totalPages: cxt.data.searchEvents.totalPages,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    file: async (
      req: RequestWithMeta<ReqTypes.GetFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.getFile)
        .then(
          A.send<ResTypes.GetFileResponse, responses.getFile>((cxt) => ({
            file: cxt.data.file,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    files: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
        validations: [
          { schema: schemas.get.query.files, validationData: req.query },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.searchFiles)
        .then(
          A.send<ResTypes.SearchFilesResponse, responses.searchFiles>(
            (cxt) => ({
              results: cxt.data.searchFiles.searchResults,
              currentPage: cxt.data.searchFiles.currentPage,
              pageSize: cxt.data.searchFiles.pageSize,
              totalCount: cxt.data.searchFiles.totalCount,
              totalPages: cxt.data.searchFiles.totalPages,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    tags: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.getTags)
        .then(
          A.send<ResTypes.GetTagsResponse, responses.getTags>((cxt) => ({
            results: cxt.data.getTags.tags,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroup: async (
      req: RequestWithMeta<ReqTypes.GetMatchGroupPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(
          A.send<ResTypes.GetMatchGroupResponse, responses.getMatchGroup>(
            (cxt) => ({
              matchGroup: {
                ...cxt.data.matchGroup,
                id: cxt.data.matchGroupId,
              },
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroups: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.query },
        validations: [
          { schema: schemas.get.query.matchGroups, validationData: req.query },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroups)
        .then(
          A.send<ResTypes.GetMatchGroupsResponse, responses.getMatchGroups>(
            (cxt) => ({
              eventId: cxt.data.matchGroups.eventId,
              results: cxt.data.matchGroups.searchResults,
              currentPage: cxt.data.matchGroups.pagination.currentPage,
              pageSize: cxt.data.matchGroups.pagination.pageSize,
              totalCount: cxt.data.matchGroups.pagination.totalCount,
              totalPages: cxt.data.matchGroups.pagination.totalPages,
              sortType: cxt.data.matchGroups.sort.field,
              sortDirection: cxt.data.matchGroups.sort.direction,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroupSelectedTracklets: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.getMatchGroupSelectedTracklets)
        .then(
          A.send<
            ResTypes.GetMatchGroupSelectedTrackletsResponse,
            Partial<responses.getMatchGroupSelectedTracklets>
          >((cxt) => {
            if (cxt.data.selectedTracklets) {
              return {
                results: cxt.data.selectedTracklets.results,
                matchGroupId: cxt.data.selectedTracklets.matchGroupId,
                matchGroupName: cxt.data.selectedTracklets.matchGroupName,
                eventId: cxt.data.selectedTracklets.eventId,
              };
            } else {
              throw new ActionError(
                'Error encountered while searching match group tracklets'
              );
            }
          })
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroupSearch: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.query },
        validations: [
          {
            schema: schemas.get.query.matchGroupSearch,
            validationData: req.query,
          },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.searchTracklets)
        .then(
          A.send<
            ResTypes.GetMatchGroupSearchResultsResponse,
            | responses.searchMatchTracklets
            | responses.searchFileTracklets
            | Partial<responses.getEvent>
          >((cxt) => {
            if ('matchGroupSearch' in cxt.data) {
              return {
                results: cxt.data.matchGroupSearch.results,
                type: cxt.data.matchGroupSearch.type,
                referenceTrackletId:
                  cxt.data.matchGroupSearch.referenceTrackletId,
                searchId: cxt.data.matchGroupSearch.searchId,
                searchName: cxt.data.matchGroupSearch.searchName,
                searchTime: cxt.data.matchGroupSearch.searchTime,
                matchGroupId: cxt.data.matchGroupSearch.matchGroupId,
                matchGroupName: cxt.data.matchGroupSearch.matchGroupName,
                eventId: cxt.data.matchGroupSearch.eventId,
                currentPage: cxt.data.matchGroupSearch.currentPage,
                pageSize: cxt.data.matchGroupSearch.pageSize,
                totalCount: cxt.data.matchGroupSearch.totalCount,
                totalPages: cxt.data.matchGroupSearch.totalPages,
                tdoIds: cxt.data.matchGroupSearch.tdoIds,
                fileNames: cxt.data.matchGroupSearch.fileNames,
                allFiles: cxt.data.matchGroupSearch.allTdos,
              };
            } else {
              throw new ActionError(
                'Error encountered while searching match group tracklets'
              );
            }
          })
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    fileResultsSearch: async (
      req: RequestWithMeta<ReqTypes.GetFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.query },
        validations: [
          {
            schema: schemas.get.query.fileResults,
            validationData: req.query,
          },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getFile)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.searchTracklets)
        .then(
          A.send<
            ResTypes.GetFileSearchResultsResponse,
            | responses.searchMatchTracklets
            | responses.searchFileTracklets
            | Partial<responses.getEvent>
          >((cxt) => {
            if ('fileSearch' in cxt.data) {
              return {
                results: cxt.data.fileSearch.results,
                type: cxt.data.fileSearch.type,
                fileId: cxt.data.fileSearch.matchGroupId,
                eventId: cxt.data.fileSearch.eventId,
                currentPage: cxt.data.fileSearch.currentPage,
                pageSize: cxt.data.fileSearch.pageSize,
                totalCount: cxt.data.fileSearch.totalCount,
                totalPages: cxt.data.fileSearch.totalPages,
              };
            } else {
              throw new ActionError('Error encountered while searching files');
            }
          })
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    fileResultsSearchIntersectingBox: async (
      req: RequestWithMeta<ReqTypes.GetFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.query },
        validations: [
          {
            schema: schemas.get.query.fileResults,
            validationData: req.query,
          },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getFile)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.searchTrackletsWithIntersectingBox)
        .then(
          A.send<
            ResTypes.SearchTrackletsWithIntersectingBoxResponse,
            SearchTrackletsWithIntersectingBoxData
          >(({ data }) => ({
            results: data.searchData.results,
            type: data.searchData.type,
            fileId: data.searchData.fileId,
            currentPage: data.searchData.currentPage,
            pageSize: data.searchData.pageSize,
            totalCount: data.searchData.totalCount,
            totalPages: data.searchData.totalPages,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    boundingBoxes: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
        validations: [
          {
            schema: schemas.get.query.boundingBoxes,
            validationData: req.query,
          },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getBoundingBoxes)
        .then(
          A.send<
            ResTypes.GetBoundingBoxesResponse,
            { boundingBoxes: TrackletBoundingBox[] }
          >((cxt) => ({
            results: cxt.data.boundingBoxes,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
  },
  patch: {
    event: async (
      req: RequestWithMeta<ReqTypes.UpdateEventPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.patch.body.event, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.updateFolder)
        .then(A.updateStructuredData)
        .then(A.getMatchGroups)
        .then(A.searchFiles)
        .then(
          A.send<
            ResTypes.UpdateEventResponse,
            responses.createStructuredData &
              responses.getMatchGroups &
              responses.searchFiles
          >((cxt) => ({
            event: {
              ...cxt.data.createStructuredData.data,
              matchGroupsCount: cxt.data.matchGroups.pagination.totalCount,
              filesCount: cxt.data.searchFiles.totalCount,
            },
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    file: async (
      req: RequestWithMeta<ReqTypes.UpdateFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.patch.body.file, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getFile)
        .then(A.updateFile)
        .then(
          A.send<ResTypes.UpdateFileResponse, responses.getFile>((cxt) => ({
            file: cxt.data.file,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroup: async (
      req: RequestWithMeta<ReqTypes.UpdateMatchGroupPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.patch.body.matchGroup, validationData: req.body },
        ],
      };
      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.updateLastSearchWithEngineId)
        .then(A.getMatchGroup)
        .then(A.updateMatchGroupStructuredData)
        .then(
          A.send<
            ResTypes.UpdateMatchGroupResponse,
            responses.structuredMatchGroupData
          >((cxt) => ({
            matchGroup: cxt.data.createStructuredData.data,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    generateTimelines: async (
      req: RequestWithMeta<ReqTypes.GenerateTimeLine>,
      res: Response,
      _next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.patch.body.matchGroup, validationData: req.body },
        ],
      };
      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.createTimelineJob)
        .then(A.saveGeneratedTimeline)
        .then(A.getMatchGroup)
        .then(A.updateMatchGroupStructuredData)
        .then(
          A.send<
            ResTypes.UpdateMatchGroupResponse,
            responses.structuredMatchGroupData
          >((cxt) => ({
            matchGroup: cxt.data.createStructuredData.data,
          }))
        )

        .catch((e) => A.sendError(cxt)(e));
    },
  },
  post: {
    importEvent: async (
      req: RequestWithMeta<ReqTypes.ImportFolderPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.post.body.importFolder, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getMe)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.getParentFolderId)
        .then(A.getAllEventSchemaIds)
        .then(A.importEvent)
        .then(A.createContentTemplate)
        .then(
          A.send<ResTypes.CreateEventResponse, responses.createStructuredData>(
            (cxt) => ({
              event: cxt.data.createStructuredData.data,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    event: async (
      req: RequestWithMeta<ReqTypes.CreateEventPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {
          ...req.body,
          currentTime: new Date().toISOString(),
        },
        validations: [
          { schema: schemas.post.body.event, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getMe)
        .then(A.checkCachedSchemaIdEvents)
        .then(A.getParentFolderId)
        .then(A.createFolder)
        .then(A.getAllEventSchemaIds)
        .then(A.createStructuredData)
        .then(A.createContentTemplate)
        .then(
          A.send<ResTypes.CreateEventResponse, responses.createStructuredData>(
            (cxt) => ({
              event: cxt.data.createStructuredData.data,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    file: async (
      req: RequestWithMeta<ReqTypes.UploadFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.body, currentTime: new Date().toISOString() },
        validations: [
          { schema: schemas.post.body.file, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getMe)
        .then(A.getSignedWritableUrl)
        .then(A.createFileTemporalData)
        .then(
          A.send<
            ResTypes.UploadFileResponse,
            responses.getSignedWritableUrl & responses.createFileTemporalData
          >((cxt) => ({
            uploadUrl: cxt.data.getSignedWritableUrl.url,
            fileId: cxt.data.temporalData.id,
            getUrl: cxt.data.getSignedWritableUrl.getUrl,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    ingest: async (
      req: RequestWithMeta<ReqTypes.IngestFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redisWrapper,
        data: { ...req.body },
        validations: [
          { schema: schemas.post.body.ingest, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getMe)
        .then(A.getFile)
        .then(A.getAllEventSchemaIds)
        .then(A.getEvent)
        .then(A.ingestFile)
        .then(
          A.send<ResTypes.IngestFileResponse, object>((_cxt) => ({
            message: 'File ingestion started',
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroup: async (
      req: RequestWithMeta<ReqTypes.CreateMatchGroupPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redisWrapper,
        data: req.body,
        validations: [
          { schema: schemas.post.body.matchGroup, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getMe)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.createMatchGroupStructuredData)
        .then(
          A.send<
            ResTypes.CreateMatchGroupResponse,
            responses.structuredMatchGroupData
          >((cxt) => ({
            matchGroup: cxt.data.createStructuredData.data,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    thumbnails: async (
      req: RequestWithMeta<ReqTypes.GetThumbnailsResponse>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: req.body,
        validations: [
          {
            schema: schemas.post.body.thumbnails,
            validationData: req.body,
          },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.validateRequest)
        .then(A.getThumbnails)
        .then(
          A.send<ResTypes.GetThumbnailsResponse, responses.getThumbnails>(
            (cxt) => ({
              thumbnails: cxt.data.thumbnails,
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    rootFolder: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {
          ...req.body,
        },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCreateRootFolder)
        .then(
          A.send<
            ResTypes.CheckCreateRootFolderResponse,
            {
              rootFolder: {
                rootFolderId: string;
                status: CheckCreateRootFolderStatus;
              };
            }
          >((cxt) => ({
            rootFolderId: cxt.data.rootFolder.rootFolderId,
            status: cxt.data.rootFolder.status,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
  },
  delete: {
    event: async (
      req: RequestWithMeta<ReqTypes.DeleteEventPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getFolder)
        .then(A.searchFiles)
        .then(A.getMatchGroups)
        .then(A.getAllEventSchemaIds)
        .then(A.deleteFiles)
        .then(A.deleteMatchGroups)
        .then(A.deleteContentTemplate)
        .then(A.deleteStructuredData)
        .then(A.deleteFolder)
        .then(
          A.send<
            ResTypes.DeleteEventResponse,
            ReqTypes.DeleteEventPayload & responses.getFolder
          >((cxt) => ({
            id: cxt.data.eventId,
            message: `Successfully deleted event ${cxt.data.folder.name}`,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    file: async (
      req: RequestWithMeta<ReqTypes.DeleteFilePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getFile)
        .then(A.searchTracklets)
        .then(A.deleteTracklets)
        .then(A.deleteTemporalData)
        .then((cxt) => {
          if (cxt.data?.file) {
            return Promise.resolve({
              ...cxt,
              data: { ...cxt.data, eventId: cxt.data.file.eventId },
            })
              .then(A.getMe)
              .then(A.searchFiles)
              .then((cxt) =>
                A.updateRedisEvent({
                  ...cxt,
                  data: {
                    ...cxt.data,
                    redisEvent: {
                      filesCount: cxt.data.filesCount,
                    },
                  },
                })
              );
          }
          return cxt;
        })
        .then(
          A.send<ResTypes.DeleteFileResponse, ReqTypes.DeleteFilePayload>(
            (cxt) => ({
              fileId: cxt.data.fileId,
              message: 'File successfully deleted',
            })
          )
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroup: async (
      req: RequestWithMeta<ReqTypes.DeleteMatchGroupPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        redis,
        redisWrapper,
        data: { matchGroupId: req.params.matchGroupId },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.deleteMatchGroup)
        .then((cxt) => {
          if (cxt.data.matchGroup) {
            return Promise.resolve({
              ...cxt,
              data: { ...cxt.data, eventId: cxt.data.matchGroup.eventId },
            })
              .then(A.getMe)
              .then(A.getMatchGroups)
              .then((cxt) =>
                A.updateRedisEvent({
                  ...cxt,
                  data: {
                    ...cxt.data,
                    redisEvent: {
                      matchGroupsCount:
                        cxt.data.matchGroups.pagination.totalCount,
                    },
                  },
                })
              );
          }
          return cxt;
        })
        .then(
          A.send<
            ResTypes.DeleteMatchGroupResponse,
            ReqTypes.DeleteMatchGroupPayload
          >((cxt) => ({
            matchGroupId: cxt.data.matchGroupId,
            message: 'MatchGroup successfully deleted',
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroupSearch: async (
      req: RequestWithMeta<ReqTypes.DeleteMatchGroupSearchPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.deleteMatchGroupSearch)
        .then(
          A.send<
            ResTypes.DeleteMatchGroupSearchResponse,
            ReqTypes.DeleteMatchGroupSearchPayload
          >((cxt) => ({
            matchGroupId: cxt.data.matchGroupId,
            searchId: cxt.data.searchId,
            message: 'Match Group Search successfully deleted',
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    timeline: async (
      req: RequestWithMeta<ReqTypes.DeleteGeneratedTimelinePayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.body },
        validations: [
          { schema: schemas.delete.query.timeline, validationData: req.body },
        ],
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.getMe)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.deleteGeneratedTimeline)
        .then(A.updateMatchGroupStructuredData)
        .then((cxt) => {
          if (cxt?.data.generatedTimelineId) {
            A.deleteTemporalData({
              ...cxt,
              data: { fileId: cxt.data.generatedTimelineId },
            });
            return cxt;
          }
        })
        .then(
          A.send<
            ResTypes.DeleteGeneratedTimelineResponse,
            ReqTypes.DeleteGeneratedTimelinePayload
          >((cxt) => ({
            matchGroup: cxt.data.matchGroup,
            generatedTimelineId: cxt.data.generatedTimelineId,
            message: `Generated timeline ${cxt.data.matchGroup.name} is pending deletion...`,
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    matchGroupSelectedTracklets: async (
      req: RequestWithMeta<ReqTypes.DeleteTrackletsPayload>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: { ...req.body },
      };

      Promise.resolve(cxt)
        .then(A.validateToken)
        .then(A.checkCachedSchemaIdMatchGroups)
        .then(A.getMatchGroup)
        .then(A.deleteTracklets)
        .then(
          A.send<
            ResTypes.DeleteTrackletsResponse,
            ReqTypes.DeleteTrackletsPayload
          >((cxt) => ({
            trackletIds: cxt.data.trackletIds,
            message: 'Successfully deleted selected detections',
          }))
        )
        .catch((e) => A.sendError(cxt)(e))
        .finally(next);
    },
    // _events: async (
    //   req: RequestWithMeta<{}>,
    //   res: Response,
    //   next: NextFunction
    // ) => {
    //   const cxt = {
    //     req,
    //     res,
    //     log,
    //     cache,
    //     data: {},
    //     validations: [
    //       { schema: schemas.delete.query._events, validationData: req.query },
    //     ],
    //   };
    //
    //   Promise.resolve(cxt)
    //     .then(A.validateToken)
    //     .then(A.validateRequest)
    //     .then(A.checkCachedSchemaIdEvents)
    //     .then(A.searchEvents)
    //     .then(A.deleteEvents)
    //     .then(
    //       A.send<ResTypes.DeleteEventResponse, ReqTypes.DeleteEventPayload>(() => ({
    //         id: cxt.data.eventId,
    //         message: 'Successfully deleted all events and event files',
    //       }))
    //     )
    //     .catch((e) => {
    //       switch (e.constructor) {
    //         case E.ValidationError:
    //           return A.sendError(cxt)(400)(e);
    //         case E.ForbiddenError:
    //           return A.sendError(cxt)(403)(e);
    //         case E.UnauthorizedError:
    //           return A.sendError(cxt)(401)(e);
    //         case E.GraphQLError:
    //         default:
    //           return A.sendError(cxt)(500)(e);
    //       }
    //     })
    //     .finally(next);
    // },
  },
});

export default createHandlers;
