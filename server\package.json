{"name": "tracker-backend", "version": "1.0.0", "description": "Rest api for tracker", "main": "index.ts", "repository": "https://github.com/veritone/tracker-app", "license": "MIT", "scripts": {"-": "------ Run following command once before development to mkcert ------", "init:env": "mkcert -install && mkcert local.veritone.com", "_": "------ Run following command for local development ------", "start:redis": "bat ./redis.sh", "start:dev": "nodemon -e ts  --exec \"npm run compile\"", "start:dev:with-redis": "yarn start:redis && nodemon -e ts  --exec \"npm run compile\"", "compile": "rimraf dist && tsc && copyfiles src/**/*.json dist/server/ && node dist/server/src/start-server.js --key ./local.veritone.com-key.pem --cert ./local.veritone.com.pem", "start:prod": "node dist/src/start-server.js", "build": "tsc --project ./tsconfig.build.json", "lint": "eslint . && yarn lint:tsc", "lint:tsc": "tsc --noEmit", "lint:fix": "eslint --fix --ext .ts ./src/", "test": "jest", "coverage": "jest --coverage", "test:watch": "jest --watch", "prepare": "cd ../ && yarn && yarn husky install"}, "dependencies": {"@aws-sdk/client-s3": "^3.670.0", "@aws-sdk/client-sts": "^3.670.0", "@aws-sdk/credential-provider-node": "^3.670.0", "@aws-sdk/s3-request-presigner": "^3.670.0", "@azure/storage-blob": "^12.25.0", "@veritone/core-logger": "^1.0.1", "@veritone/functional-permissions-lib": "^1.0.15", "axios": "^1.8.2", "body-parser": "^1.20.3", "chalk": "^4.1.2", "colors": "^1.4.0", "cors": "^2.8.5", "express": "^4.21.1", "form-data": "^4.0.4", "form-urlencoded": "^6.1.5", "graphql": "^16.9.0", "graphql-request": "^6.1.0", "http": "^0.0.1-security", "http-errors": "^2.0.0", "https": "^1.0.0", "ioredis": "^5.4.1", "ioredis-mock": "^8.9.0", "joi": "^17.13.3", "lodash": "^4.17.21", "luxon": "^3.6.1", "minimist": "^1.2.8", "module-alias": "^2.2.3", "node-cache": "^5.1.2", "node-mocks-http": "^1.16.1", "openapi-types": "^12.1.3", "p-iteration": "^1.1.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@types/chalk": "^2.2.0", "@types/copyfiles": "^2.4.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-busboy": "^8.0.3", "@types/http-errors": "^2.0.4", "@types/ioredis-mock": "^8.2.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/luxon": "^3.6.2", "@types/minimist": "^1.2.5", "@types/module-alias": "^2.0.4", "@types/node": "^22.15.31", "@types/supertest": "^6.0.3", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "copyfiles": "^2.4.1", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "jest-dom": "^4.0.0", "nodemon": "^3.1.9", "prettier": "^3.3.3", "rimraf": "^6.0.1", "supertest": "^7.0.0", "ts-essentials": "^10.0.4", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-files": "^1.1.4", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}, "resolutions": {"colors": "^1.4.0", "jsonwebtoken": "^9.0.2", "path-to-regexp": "0.1.12", "cross-spawn": "^7.0.5", "@types/node": "^22.15.31"}, "packageManager": "yarn@4.1.1", "_moduleAliases": {"@util": "dist/server/src/util", "@common": "dist/server/src/application/common", "@tracker": "dist/server/src/application/tracker", "@application": "dist/server/src/application"}}