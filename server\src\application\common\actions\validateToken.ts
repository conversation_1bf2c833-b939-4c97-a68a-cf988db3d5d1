import { callGQL } from '../../../util/api/graphQL/callGraphQL';
import { ForbiddenError, UnauthorizedError } from '@common/errors';
import { Context } from '../../types';
import axios from 'axios';
import AppNotEnabledForUser from '@common/errors/AppNotEnabledForUser';
import env from '../../../env';

interface ValidateTokenResponse {
  validateToken: {
    token: string;
  };
}

interface ApplicationResponse {
  data: {
    totalResults: number;
    from: number;
    to: number;
    results: Application[];
  };
}

interface Application {
  applicationId: string;
  applicationName: string;
  applicationKey: string;
  applicationStatus: 'active' | 'draft' | string;
  applicationDescription: string;
  applicationIconUrl: string;
  applicationIconSvg: string | null;
  applicationUrl: string;
  oauth2RedirectUrls: string | null;
  createdDate: string;
  updatedDate: string;
  billingPlanId: string | null;
  eventEndpoint: string | null;
  ownerOrganizationId: number;
  deploymentModel: number;
  sharedWithOrganizationId: number | null;
  applicationCheckPermissions: boolean;
  public: boolean;
  headerbarEnabled: boolean;
  signedApplicationIconUrl?: string;
}

const validateTokenQuery = (token: string) => `
  mutation validateToken {
    validateToken(token:"${token}") {
      token
    }
  }
`;

const validateToken = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
) => {
  const { req, log } = context;

  const bearerHeader = req.get('Authorization');

  log.debug('bearerHeader', bearerHeader);

  if (bearerHeader) {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];
    if (bearerToken) {
      try {
        const query = validateTokenQuery(bearerToken);
        const headers = { Authorization: req.headers.authorization };
        const response = await callGQL<ValidateTokenResponse, ReqPayload, Data>(
          context,
          headers,
          query
        );

        const appResponse = await axios.get<unknown, ApplicationResponse>(
          `${env.apiRoot}/v1/admin/current-user/applications`,
          {
            headers: {
              Authorization: `Bearer ${bearerToken}`,
            },
          }
        );

        const hasApp = appResponse.data.results.find(
          (r) => r.applicationName === 'Track'
        );

        if (!hasApp) {
          throw new AppNotEnabledForUser(
            'Track application not available to user'
          );
        }

        if (response?.validateToken?.token) {
          return context;
        }
      } catch (_err) {
        log.error('API Token Validation failed: Forbidden');
        throw new ForbiddenError();
      }
    } else {
      log.error('API Token Validation failed: Bearer token not found');
      throw new UnauthorizedError();
    }
  } else {
    log.error('API Token Validation failed: Bearer header not provided');
    throw new UnauthorizedError();
  }
};

export default validateToken;
