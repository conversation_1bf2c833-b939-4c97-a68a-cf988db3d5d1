import { Given, Before, Then } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';
import '../activeTab/activeTab.step';

Before({ tags: '@file-screen' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/files\/\?/).as('fetchFiles');
});

Given('The user is on File Screen', () => {
  mainPage.visit();
  mainPage.clickTab("Files");
});

Then('The user should see {string} in the file table', (fileName: string) => {
  mainPage.verifyFileInTable(fileName);
});

Then('The file {string} should not be in the file table', (fileName: string) => {
  mainPage.verifyFileNotInTable(fileName);
});
