import axios from 'axios';

jest.mock('ioredis', () => require('ioredis-mock'));
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock successful axios response with Track app
mockedAxios.get.mockResolvedValue({
  data: {
    totalResults: 2,
    from: 0,
    to: 2,
    results: [
      {
        applicationId: '1',
        applicationName: 'Other App',
        applicationKey: 'other-app',
        applicationStatus: 'active',
        applicationDescription: 'Other application',
        applicationIconUrl: 'https://example.com/icon.png',
        applicationIconSvg: null,
        applicationUrl: 'https://example.com',
        oauth2RedirectUrls: null,
        permissionsRequired: null,
        createdDate: '2023-01-01',
        updatedDate: '2023-01-01',
        billingPlanId: null,
        eventEndpoint: null,
        ownerOrganizationId: 1,
        deploymentModel: 1,
        sharedWithOrganizationId: null,
        applicationCheckPermissions: false,
        public: false,
        headerbarEnabled: false,
      },
      {
        applicationId: '2',
        applicationName: 'Track',
        applicationKey: 'track-app',
        applicationStatus: 'active',
        applicationDescription: 'Track application',
        applicationIconUrl: 'https://example.com/track-icon.png',
        applicationIconSvg: null,
        applicationUrl: 'https://track.example.com',
        oauth2RedirectUrls: null,
        permissionsRequired: null,
        createdDate: '2023-01-01',
        updatedDate: '2023-01-01',
        billingPlanId: null,
        eventEndpoint: null,
        ownerOrganizationId: 1,
        deploymentModel: 1,
        sharedWithOrganizationId: null,
        applicationCheckPermissions: false,
        public: false,
        headerbarEnabled: false,
      },
    ],
  },
});
