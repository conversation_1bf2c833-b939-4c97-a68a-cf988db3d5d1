import NodeCache from 'node-cache';
import updateFolder from '../updateFolder';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import * as ResTypes from '../../../../../../types/responses';
import RedisWrapper from '../../../../redisWrapper';
import Redis from 'ioredis-mock';

let cxt: Context<
  object,
  responses.getEvent &
    ResTypes.UpdateEventPayloadResponse & { eventSchemaIds: string[] }
>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createStructuredData: {} })
  ),
}));

describe('Update Folder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        name: 'name',
        folder: {
          id: 'folderId',
          contentTemplates: [
            {
              sdo: {
                id: 'id',
                schemaId: 'schemaId',
                data: {
                  tags: [],
                  id: '',
                  name: '',
                  createdBy: '',
                  createdByName: '',
                  description: '',
                  eventStartDate: '',
                  eventEndDate: '',
                  createdDateTime: '',
                  modifiedDateTime: '',
                  matchGroupsCount: 0,
                  filesCount: 0,
                },
              },
              id: '',
            },
          ],
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'organizationId',
            },
          },
        },
        event: {
          eventEndDate: '2024-04-10T17:02:10Z',
          createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
          createdByName: 'Test User',
          name: 'Event Test 1',
          description: 'New description',
          createdDateTime: '2024-04-11T00:10:13.876Z',
          modifiedDateTime: '2024-04-11T00:10:24.681Z',
          id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
          eventStartDate: '2024-04-10T17:02:10Z',
          tags: ['Tag 1', 'Tag 2'],
          matchGroupsCount: 14,
          filesCount: 13,
        },
        currentTime: '',
        eventSchemaIds: [],
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      // @ts-expect-error TODO: Why are we passing unassigned clientRedis?
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
    cxt.cache.set('eventsSchemaId', 'eventsSchemaId');
  });

  it('Updates a folder with adequate data', async () => {
    const response = await updateFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        name: 'name',
        folderId: 'folderId',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no name provided', async () => {
    cxt.data.name = undefined;

    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is no folder provided', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.folder = undefined;

    expect(async () => await updateFolder(cxt)).rejects.toThrowError(
      'Missing folder'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
