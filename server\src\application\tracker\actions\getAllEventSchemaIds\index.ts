import { Context } from '../../../types';
import { queries, responses } from '@tracker/graphQL';
import { ActionError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import env from '../../../../env';
import { SchemasResponse } from '../checkCachedSchemaIds';

const getAllEventSchemaIds = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & { eventSchemaIds: string[] }>> => {
  const { cache, data } = context;
  const headers = { Authorization: context.req.headers.authorization };
  const {
    registryIds: { eventsRegistryId },
  } = env;

  let eventSchemaIds;

  try {
    eventSchemaIds = cache.get<string[] | undefined>('eventSchemaIds');
  } catch (e) {
    console.error('CACHE ERROR ', e.message);
  }

  if (eventSchemaIds) {
    const new_data = Object.assign({}, data, { eventSchemaIds });
    return Object.assign({}, context, { data: new_data });
  }

  let schemaIdsByName: string[] = [];
  let dataRegistry: responses.dataRegistry['dataRegistry'] | null = null;

  try {
    const dataRegResponse = await callGQL<
      responses.dataRegistry,
      ReqPayload,
      Data
    >(context, headers, queries.fetchAllSchemaIds, {
      dataRegistryId: eventsRegistryId,
    });

    dataRegistry = dataRegResponse.dataRegistry;
  } catch (e) {
    if (e.message.includes('The requested object was not found')) {
      const resp = await callGQL<SchemasResponse, ReqPayload, Data>(
        context,
        headers,
        queries.getSchemaIdsByDataRegistryName('track-event')
      );

      schemaIdsByName = resp.schemas?.records.map((r) => r.id);

      if (!schemaIdsByName || schemaIdsByName.length === 0) {
        throw new ActionError('Could not retrieve data registry schema');
      }
    }
  }

  const schemaIds =
    dataRegistry?.schemas?.records.map(({ id }) => id) ?? schemaIdsByName;

  cache.set(`eventSchemaIds`, schemaIds);

  if (!schemaIds || schemaIds.length === 0) {
    throw new ActionError('No schema IDs found in the data registry');
  }

  const new_data = Object.assign({}, data, {
    eventSchemaIds: schemaIds,
  });

  return Object.assign({}, context, { data: new_data });
};

export default getAllEventSchemaIds;
