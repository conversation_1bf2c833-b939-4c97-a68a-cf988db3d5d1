import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import deleteStructuredData from '../deleteStructuredData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { queries, responses } from '@tracker/graphQL';
import RedisWrapper from '../../../../redisWrapper';
import Redis from 'ioredis-mock';

let cxt: Context<object, responses.getFolder & { eventSchemaIds?: string[] }>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

// jest.mock('@util/api/graphQL/callGraphQL', () => ({
//   callGQL: jest.fn(
//     (
//       _context: Context<object, object>,
//       _headers: RequestHeader,
//       query: string,
//       variables?: Variables
//     ) => {
//       if (query.includes('validateToken')) {
//         return Promise.resolve({
//           validateToken: {
//             token: 'validToken',
//           },
//         });
//       }
//       if (query.includes(queries.fetchAllSchemaIds)) {
//         return Promise.resolve({
//           dataRegistry: {
//             schemas: {
//               records: [
//                 {
//                   id: 'schema1'
//                 },
//                 {
//                   id: 'schema2'
//                 }
//               ]
//             }
//           },
//         });
//       }
//     }
//   ),
// }));

describe('Delete Structured Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        eventSchemaIds: ['schemaId'],
        folder: {
          id: 'folderId',
          contentTemplates: [
            {
              id: 'id',
              sdo: {
                id: 'sdoId',
                data: {
                  id: '',
                  name: '',
                  tags: [],
                  createdBy: '',
                  createdByName: '',
                  description: '',
                  eventStartDate: '',
                  eventEndDate: '',
                  createdDateTime: '',
                  modifiedDateTime: '',
                  matchGroupsCount: 0,
                  filesCount: 0,
                },
                schemaId: 'schemaId',
              },
            },
          ],
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      // @ts-expect-error TODO: Why are we passing unassigend clientRedis?
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Successfully deletes structured data', async () => {
    const response = await deleteStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { sdoId: 'sdoId', schemaId: 'schemaId' }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no folder', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.folder = undefined;

    expect(async () => await deleteStructuredData(cxt)).rejects.toThrowError(
      'No folder provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
