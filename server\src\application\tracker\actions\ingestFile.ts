import { Context } from '../../types';
import { queries, responses } from '../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import env from '../../../env';
import * as ResTypes from '../../../../../types/responses';

const ingestFile = async <
  ReqPayload,
  Data extends ResTypes.IngestFilePayloadResponse &
    responses.getMe &
    Partial<responses.getEvent>,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, req, log, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };
  const {
    gpuClusterId,
    outputWriterEngineId,
    glcIngestorEngineId,
    credentialApi,
    adaptiveBoxPoolingThreshold,
  } = env;
  const { getUrl, userEmail, fileType, event } = data;
  const { fileId } = req.params;

  // Pull the engine id stored in the event, otherwise use the default.
  // Do not ever change the default engine GUID. This will break old events that do not store the engine id.
  const trackerEngineId =
    event?.trackerEngineId ??
    event?.trackerEngineId ??
    'd77d6133-a801-472c-bc7e-48ddafec8590';

  if (
    !getUrl ||
    !userEmail ||
    !fileId ||
    !glcIngestorEngineId ||
    !trackerEngineId ||
    !outputWriterEngineId
  ) {
    throw new ActionError(
      `Missing required data: ${[!getUrl && 'getUrl', !userEmail && 'userEmail', !fileId && 'fileId', !glcIngestorEngineId && 'glcIngestorEngineId', !trackerEngineId && 'trackerEngineId', !outputWriterEngineId && 'outputWriterEngineId'].filter(Boolean).join(', ')}`
    );
  }

  const hasCluster = !!gpuClusterId;

  const variables = {
    tdoId: fileId,
    clusterId: gpuClusterId,
    glcIngestorEngineId,
    glcIngestorPayload: {
      fileType: fileType,
      tdoId: fileId,
      url: getUrl,
      user: userEmail,
      chunkDuration: 600,
      chunkOverlap: 0,
    },
    trackerEngineId,
    trackerEnginePayload: {} as {
      adaptiveBoxPoolingThreshold: number;
      storageCredentialAPIUrl?: string;
    },
    outputWriterEngineId,
  };
  if (credentialApi) {
    variables.trackerEnginePayload.storageCredentialAPIUrl = credentialApi;
  } else {
    log.info(
      'credentialApi not set. trackerEnginePayload will no include storageCredentialAPIUrl'
    );
  }

  variables.trackerEnginePayload.adaptiveBoxPoolingThreshold =
    adaptiveBoxPoolingThreshold ?? 10000;

  try {
    const res = await callGQL<responses.createJob, ReqPayload, Data>(
      context,
      headers,
      queries.createIngestJob(hasCluster),
      variables
    );
    if (redisWrapper && data.folder && event) {
      await redisWrapper.event.del(
        event.id,
        data.folder.parent.organization.id
      );
    }
    log.debug(`Ingest Job created: ${JSON.stringify(res)}`);
    return context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default ingestFile;
